2235264766336
941587210
D:/Unity/司导/SIP2.0/Assets/Scripts/SIPClient.cs
5
7
TMPro
106
UNITY_2020_3_48
UNITY_2020_3
UNITY_2020
UNITY_5_3_OR_NEWER
UNITY_5_4_OR_NEWER
UNITY_5_5_OR_NEWER
UNITY_5_6_OR_NEWER
UNITY_2017_1_OR_NEWER
UNITY_2017_2_OR_NEWER
UNITY_2017_3_OR_NEWER
UNITY_2017_4_OR_NEWER
UNITY_2018_1_OR_NEWER
UNITY_2018_2_OR_NEWER
UNITY_2018_3_OR_NEWER
UNITY_2018_4_OR_NEWER
UNITY_2019_1_OR_NEWER
UNITY_2019_2_OR_NEWER
UNITY_2019_3_OR_NEWER
UNITY_2019_4_OR_NEWER
UNITY_2020_1_OR_NEWER
UNITY_2020_2_OR_NEWER
UNITY_2020_3_OR_NEWER
PLATFORM_ARCH_64
UNITY_64
UNITY_INCLUDE_TESTS
ENABLE_AR
ENABLE_AUDIO
ENABLE_CACHING
ENABLE_CLOTH
ENABLE_EVENT_QUEUE
ENABLE_MICROPHONE
ENABLE_MULTIPLE_DISPLAYS
ENABLE_PHYSICS
ENABLE_TEXTURE_STREAMING
ENABLE_VIRTUALTEXTURING
ENABLE_UNET
ENABLE_LZMA
ENABLE_UNITYEVENTS
ENABLE_VR
ENABLE_WEBCAM
ENABLE_UNITYWEBREQUEST
ENABLE_WWW
ENABLE_CLOUD_SERVICES
ENABLE_CLOUD_SERVICES_COLLAB
ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
ENABLE_CLOUD_SERVICES_ADS
ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
ENABLE_CLOUD_SERVICES_CRASH_REPORTING
ENABLE_CLOUD_SERVICES_PURCHASING
ENABLE_CLOUD_SERVICES_ANALYTICS
ENABLE_CLOUD_SERVICES_UNET
ENABLE_CLOUD_SERVICES_BUILD
ENABLE_CLOUD_LICENSE
ENABLE_EDITOR_HUB_LICENSE
ENABLE_WEBSOCKET_CLIENT
ENABLE_DIRECTOR_AUDIO
ENABLE_DIRECTOR_TEXTURE
ENABLE_MANAGED_JOBS
ENABLE_MANAGED_TRANSFORM_JOBS
ENABLE_MANAGED_ANIMATION_JOBS
ENABLE_MANAGED_AUDIO_JOBS
ENABLE_MANAGED_UNITYTLS
INCLUDE_DYNAMIC_GI
ENABLE_MONO_BDWGC
ENABLE_SCRIPTING_GC_WBARRIERS
PLATFORM_SUPPORTS_MONO
RENDER_SOFTWARE_CURSOR
ENABLE_VIDEO
PLATFORM_STANDALONE
PLATFORM_STANDALONE_WIN
UNITY_STANDALONE_WIN
UNITY_STANDALONE
UNITY_UGP_API
ENABLE_RUNTIME_GI
ENABLE_MOVIES
ENABLE_NETWORK
ENABLE_CRUNCH_TEXTURE_COMPRESSION
ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
ENABLE_CLUSTER_SYNC
ENABLE_CLUSTERINPUT
PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
ENABLE_WEBSOCKET_HOST
ENABLE_MONO
NET_4_6
ENABLE_PROFILER
DEBUG
TRACE
UNITY_ASSERTIONS
UNITY_EDITOR
UNITY_EDITOR_IG
UNITY_EDITOR_64
UNITY_EDITOR_WIN
ENABLE_CLOUD_FEATURES
ENABLE_UNITY_COLLECTIONS_CHECKS
ENABLE_BURST_AOT
UNITY_TEAM_LICENSE
ENABLE_CUSTOM_RENDER_TEXTURE
ENABLE_DIRECTOR
ENABLE_LOCALIZATION
ENABLE_SPRITES
ENABLE_TERRAIN
ENABLE_TILEMAP
ENABLE_TIMELINE
ENABLE_LEGACY_INPUT_MANAGER
0
2235264766552
941587210
D:/Unity/司导/SIP2.0/Assets/Scripts/SIPClientSceneSetup.cs
3
7
TMPro
106
UNITY_2020_3_48
UNITY_2020_3
UNITY_2020
UNITY_5_3_OR_NEWER
UNITY_5_4_OR_NEWER
UNITY_5_5_OR_NEWER
UNITY_5_6_OR_NEWER
UNITY_2017_1_OR_NEWER
UNITY_2017_2_OR_NEWER
UNITY_2017_3_OR_NEWER
UNITY_2017_4_OR_NEWER
UNITY_2018_1_OR_NEWER
UNITY_2018_2_OR_NEWER
UNITY_2018_3_OR_NEWER
UNITY_2018_4_OR_NEWER
UNITY_2019_1_OR_NEWER
UNITY_2019_2_OR_NEWER
UNITY_2019_3_OR_NEWER
UNITY_2019_4_OR_NEWER
UNITY_2020_1_OR_NEWER
UNITY_2020_2_OR_NEWER
UNITY_2020_3_OR_NEWER
PLATFORM_ARCH_64
UNITY_64
UNITY_INCLUDE_TESTS
ENABLE_AR
ENABLE_AUDIO
ENABLE_CACHING
ENABLE_CLOTH
ENABLE_EVENT_QUEUE
ENABLE_MICROPHONE
ENABLE_MULTIPLE_DISPLAYS
ENABLE_PHYSICS
ENABLE_TEXTURE_STREAMING
ENABLE_VIRTUALTEXTURING
ENABLE_UNET
ENABLE_LZMA
ENABLE_UNITYEVENTS
ENABLE_VR
ENABLE_WEBCAM
ENABLE_UNITYWEBREQUEST
ENABLE_WWW
ENABLE_CLOUD_SERVICES
ENABLE_CLOUD_SERVICES_COLLAB
ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
ENABLE_CLOUD_SERVICES_ADS
ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
ENABLE_CLOUD_SERVICES_CRASH_REPORTING
ENABLE_CLOUD_SERVICES_PURCHASING
ENABLE_CLOUD_SERVICES_ANALYTICS
ENABLE_CLOUD_SERVICES_UNET
ENABLE_CLOUD_SERVICES_BUILD
ENABLE_CLOUD_LICENSE
ENABLE_EDITOR_HUB_LICENSE
ENABLE_WEBSOCKET_CLIENT
ENABLE_DIRECTOR_AUDIO
ENABLE_DIRECTOR_TEXTURE
ENABLE_MANAGED_JOBS
ENABLE_MANAGED_TRANSFORM_JOBS
ENABLE_MANAGED_ANIMATION_JOBS
ENABLE_MANAGED_AUDIO_JOBS
ENABLE_MANAGED_UNITYTLS
INCLUDE_DYNAMIC_GI
ENABLE_MONO_BDWGC
ENABLE_SCRIPTING_GC_WBARRIERS
PLATFORM_SUPPORTS_MONO
RENDER_SOFTWARE_CURSOR
ENABLE_VIDEO
PLATFORM_STANDALONE
PLATFORM_STANDALONE_WIN
UNITY_STANDALONE_WIN
UNITY_STANDALONE
UNITY_UGP_API
ENABLE_RUNTIME_GI
ENABLE_MOVIES
ENABLE_NETWORK
ENABLE_CRUNCH_TEXTURE_COMPRESSION
ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
ENABLE_CLUSTER_SYNC
ENABLE_CLUSTERINPUT
PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
ENABLE_WEBSOCKET_HOST
ENABLE_MONO
NET_4_6
ENABLE_PROFILER
DEBUG
TRACE
UNITY_ASSERTIONS
UNITY_EDITOR
UNITY_EDITOR_IG
UNITY_EDITOR_64
UNITY_EDITOR_WIN
ENABLE_CLOUD_FEATURES
ENABLE_UNITY_COLLECTIONS_CHECKS
ENABLE_BURST_AOT
UNITY_TEAM_LICENSE
ENABLE_CUSTOM_RENDER_TEXTURE
ENABLE_DIRECTOR
ENABLE_LOCALIZATION
ENABLE_SPRITES
ENABLE_TERRAIN
ENABLE_TILEMAP
ENABLE_TIMELINE
ENABLE_LEGACY_INPUT_MANAGER
0
2235264766768
941587210
D:/Unity/司导/SIP2.0/Assets/Scripts/SIPClient.cs
165
31
TextMeshProUGUI
106
UNITY_2020_3_48
UNITY_2020_3
UNITY_2020
UNITY_5_3_OR_NEWER
UNITY_5_4_OR_NEWER
UNITY_5_5_OR_NEWER
UNITY_5_6_OR_NEWER
UNITY_2017_1_OR_NEWER
UNITY_2017_2_OR_NEWER
UNITY_2017_3_OR_NEWER
UNITY_2017_4_OR_NEWER
UNITY_2018_1_OR_NEWER
UNITY_2018_2_OR_NEWER
UNITY_2018_3_OR_NEWER
UNITY_2018_4_OR_NEWER
UNITY_2019_1_OR_NEWER
UNITY_2019_2_OR_NEWER
UNITY_2019_3_OR_NEWER
UNITY_2019_4_OR_NEWER
UNITY_2020_1_OR_NEWER
UNITY_2020_2_OR_NEWER
UNITY_2020_3_OR_NEWER
PLATFORM_ARCH_64
UNITY_64
UNITY_INCLUDE_TESTS
ENABLE_AR
ENABLE_AUDIO
ENABLE_CACHING
ENABLE_CLOTH
ENABLE_EVENT_QUEUE
ENABLE_MICROPHONE
ENABLE_MULTIPLE_DISPLAYS
ENABLE_PHYSICS
ENABLE_TEXTURE_STREAMING
ENABLE_VIRTUALTEXTURING
ENABLE_UNET
ENABLE_LZMA
ENABLE_UNITYEVENTS
ENABLE_VR
ENABLE_WEBCAM
ENABLE_UNITYWEBREQUEST
ENABLE_WWW
ENABLE_CLOUD_SERVICES
ENABLE_CLOUD_SERVICES_COLLAB
ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
ENABLE_CLOUD_SERVICES_ADS
ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
ENABLE_CLOUD_SERVICES_CRASH_REPORTING
ENABLE_CLOUD_SERVICES_PURCHASING
ENABLE_CLOUD_SERVICES_ANALYTICS
ENABLE_CLOUD_SERVICES_UNET
ENABLE_CLOUD_SERVICES_BUILD
ENABLE_CLOUD_LICENSE
ENABLE_EDITOR_HUB_LICENSE
ENABLE_WEBSOCKET_CLIENT
ENABLE_DIRECTOR_AUDIO
ENABLE_DIRECTOR_TEXTURE
ENABLE_MANAGED_JOBS
ENABLE_MANAGED_TRANSFORM_JOBS
ENABLE_MANAGED_ANIMATION_JOBS
ENABLE_MANAGED_AUDIO_JOBS
ENABLE_MANAGED_UNITYTLS
INCLUDE_DYNAMIC_GI
ENABLE_MONO_BDWGC
ENABLE_SCRIPTING_GC_WBARRIERS
PLATFORM_SUPPORTS_MONO
RENDER_SOFTWARE_CURSOR
ENABLE_VIDEO
PLATFORM_STANDALONE
PLATFORM_STANDALONE_WIN
UNITY_STANDALONE_WIN
UNITY_STANDALONE
UNITY_UGP_API
ENABLE_RUNTIME_GI
ENABLE_MOVIES
ENABLE_NETWORK
ENABLE_CRUNCH_TEXTURE_COMPRESSION
ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
ENABLE_CLUSTER_SYNC
ENABLE_CLUSTERINPUT
PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
ENABLE_WEBSOCKET_HOST
ENABLE_MONO
NET_4_6
ENABLE_PROFILER
DEBUG
TRACE
UNITY_ASSERTIONS
UNITY_EDITOR
UNITY_EDITOR_IG
UNITY_EDITOR_64
UNITY_EDITOR_WIN
ENABLE_CLOUD_FEATURES
ENABLE_UNITY_COLLECTIONS_CHECKS
ENABLE_BURST_AOT
UNITY_TEAM_LICENSE
ENABLE_CUSTOM_RENDER_TEXTURE
ENABLE_DIRECTOR
ENABLE_LOCALIZATION
ENABLE_SPRITES
ENABLE_TERRAIN
ENABLE_TILEMAP
ENABLE_TIMELINE
ENABLE_LEGACY_INPUT_MANAGER
0
2235264766984
941587210
D:/Unity/司导/SIP2.0/Assets/Scripts/SIPClient.cs
170
37
TMP_InputField
106
UNITY_2020_3_48
UNITY_2020_3
UNITY_2020
UNITY_5_3_OR_NEWER
UNITY_5_4_OR_NEWER
UNITY_5_5_OR_NEWER
UNITY_5_6_OR_NEWER
UNITY_2017_1_OR_NEWER
UNITY_2017_2_OR_NEWER
UNITY_2017_3_OR_NEWER
UNITY_2017_4_OR_NEWER
UNITY_2018_1_OR_NEWER
UNITY_2018_2_OR_NEWER
UNITY_2018_3_OR_NEWER
UNITY_2018_4_OR_NEWER
UNITY_2019_1_OR_NEWER
UNITY_2019_2_OR_NEWER
UNITY_2019_3_OR_NEWER
UNITY_2019_4_OR_NEWER
UNITY_2020_1_OR_NEWER
UNITY_2020_2_OR_NEWER
UNITY_2020_3_OR_NEWER
PLATFORM_ARCH_64
UNITY_64
UNITY_INCLUDE_TESTS
ENABLE_AR
ENABLE_AUDIO
ENABLE_CACHING
ENABLE_CLOTH
ENABLE_EVENT_QUEUE
ENABLE_MICROPHONE
ENABLE_MULTIPLE_DISPLAYS
ENABLE_PHYSICS
ENABLE_TEXTURE_STREAMING
ENABLE_VIRTUALTEXTURING
ENABLE_UNET
ENABLE_LZMA
ENABLE_UNITYEVENTS
ENABLE_VR
ENABLE_WEBCAM
ENABLE_UNITYWEBREQUEST
ENABLE_WWW
ENABLE_CLOUD_SERVICES
ENABLE_CLOUD_SERVICES_COLLAB
ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
ENABLE_CLOUD_SERVICES_ADS
ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
ENABLE_CLOUD_SERVICES_CRASH_REPORTING
ENABLE_CLOUD_SERVICES_PURCHASING
ENABLE_CLOUD_SERVICES_ANALYTICS
ENABLE_CLOUD_SERVICES_UNET
ENABLE_CLOUD_SERVICES_BUILD
ENABLE_CLOUD_LICENSE
ENABLE_EDITOR_HUB_LICENSE
ENABLE_WEBSOCKET_CLIENT
ENABLE_DIRECTOR_AUDIO
ENABLE_DIRECTOR_TEXTURE
ENABLE_MANAGED_JOBS
ENABLE_MANAGED_TRANSFORM_JOBS
ENABLE_MANAGED_ANIMATION_JOBS
ENABLE_MANAGED_AUDIO_JOBS
ENABLE_MANAGED_UNITYTLS
INCLUDE_DYNAMIC_GI
ENABLE_MONO_BDWGC
ENABLE_SCRIPTING_GC_WBARRIERS
PLATFORM_SUPPORTS_MONO
RENDER_SOFTWARE_CURSOR
ENABLE_VIDEO
PLATFORM_STANDALONE
PLATFORM_STANDALONE_WIN
UNITY_STANDALONE_WIN
UNITY_STANDALONE
UNITY_UGP_API
ENABLE_RUNTIME_GI
ENABLE_MOVIES
ENABLE_NETWORK
ENABLE_CRUNCH_TEXTURE_COMPRESSION
ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
ENABLE_CLUSTER_SYNC
ENABLE_CLUSTERINPUT
PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
ENABLE_WEBSOCKET_HOST
ENABLE_MONO
NET_4_6
ENABLE_PROFILER
DEBUG
TRACE
UNITY_ASSERTIONS
UNITY_EDITOR
UNITY_EDITOR_IG
UNITY_EDITOR_64
UNITY_EDITOR_WIN
ENABLE_CLOUD_FEATURES
ENABLE_UNITY_COLLECTIONS_CHECKS
ENABLE_BURST_AOT
UNITY_TEAM_LICENSE
ENABLE_CUSTOM_RENDER_TEXTURE
ENABLE_DIRECTOR
ENABLE_LOCALIZATION
ENABLE_SPRITES
ENABLE_TERRAIN
ENABLE_TILEMAP
ENABLE_TIMELINE
ENABLE_LEGACY_INPUT_MANAGER
0
2235264767200
941587210
D:/Unity/司导/SIP2.0/Assets/Scripts/SIPClient.cs
64
12
TMP_InputField
106
UNITY_2020_3_48
UNITY_2020_3
UNITY_2020
UNITY_5_3_OR_NEWER
UNITY_5_4_OR_NEWER
UNITY_5_5_OR_NEWER
UNITY_5_6_OR_NEWER
UNITY_2017_1_OR_NEWER
UNITY_2017_2_OR_NEWER
UNITY_2017_3_OR_NEWER
UNITY_2017_4_OR_NEWER
UNITY_2018_1_OR_NEWER
UNITY_2018_2_OR_NEWER
UNITY_2018_3_OR_NEWER
UNITY_2018_4_OR_NEWER
UNITY_2019_1_OR_NEWER
UNITY_2019_2_OR_NEWER
UNITY_2019_3_OR_NEWER
UNITY_2019_4_OR_NEWER
UNITY_2020_1_OR_NEWER
UNITY_2020_2_OR_NEWER
UNITY_2020_3_OR_NEWER
PLATFORM_ARCH_64
UNITY_64
UNITY_INCLUDE_TESTS
ENABLE_AR
ENABLE_AUDIO
ENABLE_CACHING
ENABLE_CLOTH
ENABLE_EVENT_QUEUE
ENABLE_MICROPHONE
ENABLE_MULTIPLE_DISPLAYS
ENABLE_PHYSICS
ENABLE_TEXTURE_STREAMING
ENABLE_VIRTUALTEXTURING
ENABLE_UNET
ENABLE_LZMA
ENABLE_UNITYEVENTS
ENABLE_VR
ENABLE_WEBCAM
ENABLE_UNITYWEBREQUEST
ENABLE_WWW
ENABLE_CLOUD_SERVICES
ENABLE_CLOUD_SERVICES_COLLAB
ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
ENABLE_CLOUD_SERVICES_ADS
ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
ENABLE_CLOUD_SERVICES_CRASH_REPORTING
ENABLE_CLOUD_SERVICES_PURCHASING
ENABLE_CLOUD_SERVICES_ANALYTICS
ENABLE_CLOUD_SERVICES_UNET
ENABLE_CLOUD_SERVICES_BUILD
ENABLE_CLOUD_LICENSE
ENABLE_EDITOR_HUB_LICENSE
ENABLE_WEBSOCKET_CLIENT
ENABLE_DIRECTOR_AUDIO
ENABLE_DIRECTOR_TEXTURE
ENABLE_MANAGED_JOBS
ENABLE_MANAGED_TRANSFORM_JOBS
ENABLE_MANAGED_ANIMATION_JOBS
ENABLE_MANAGED_AUDIO_JOBS
ENABLE_MANAGED_UNITYTLS
INCLUDE_DYNAMIC_GI
ENABLE_MONO_BDWGC
ENABLE_SCRIPTING_GC_WBARRIERS
PLATFORM_SUPPORTS_MONO
RENDER_SOFTWARE_CURSOR
ENABLE_VIDEO
PLATFORM_STANDALONE
PLATFORM_STANDALONE_WIN
UNITY_STANDALONE_WIN
UNITY_STANDALONE
UNITY_UGP_API
ENABLE_RUNTIME_GI
ENABLE_MOVIES
ENABLE_NETWORK
ENABLE_CRUNCH_TEXTURE_COMPRESSION
ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
ENABLE_CLUSTER_SYNC
ENABLE_CLUSTERINPUT
PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
ENABLE_WEBSOCKET_HOST
ENABLE_MONO
NET_4_6
ENABLE_PROFILER
DEBUG
TRACE
UNITY_ASSERTIONS
UNITY_EDITOR
UNITY_EDITOR_IG
UNITY_EDITOR_64
UNITY_EDITOR_WIN
ENABLE_CLOUD_FEATURES
ENABLE_UNITY_COLLECTIONS_CHECKS
ENABLE_BURST_AOT
UNITY_TEAM_LICENSE
ENABLE_CUSTOM_RENDER_TEXTURE
ENABLE_DIRECTOR
ENABLE_LOCALIZATION
ENABLE_SPRITES
ENABLE_TERRAIN
ENABLE_TILEMAP
ENABLE_TIMELINE
ENABLE_LEGACY_INPUT_MANAGER
0
2235264767416
941587210
D:/Unity/司导/SIP2.0/Assets/Scripts/SIPClient.cs
65
13
TextMeshProUGUI
106
UNITY_2020_3_48
UNITY_2020_3
UNITY_2020
UNITY_5_3_OR_NEWER
UNITY_5_4_OR_NEWER
UNITY_5_5_OR_NEWER
UNITY_5_6_OR_NEWER
UNITY_2017_1_OR_NEWER
UNITY_2017_2_OR_NEWER
UNITY_2017_3_OR_NEWER
UNITY_2017_4_OR_NEWER
UNITY_2018_1_OR_NEWER
UNITY_2018_2_OR_NEWER
UNITY_2018_3_OR_NEWER
UNITY_2018_4_OR_NEWER
UNITY_2019_1_OR_NEWER
UNITY_2019_2_OR_NEWER
UNITY_2019_3_OR_NEWER
UNITY_2019_4_OR_NEWER
UNITY_2020_1_OR_NEWER
UNITY_2020_2_OR_NEWER
UNITY_2020_3_OR_NEWER
PLATFORM_ARCH_64
UNITY_64
UNITY_INCLUDE_TESTS
ENABLE_AR
ENABLE_AUDIO
ENABLE_CACHING
ENABLE_CLOTH
ENABLE_EVENT_QUEUE
ENABLE_MICROPHONE
ENABLE_MULTIPLE_DISPLAYS
ENABLE_PHYSICS
ENABLE_TEXTURE_STREAMING
ENABLE_VIRTUALTEXTURING
ENABLE_UNET
ENABLE_LZMA
ENABLE_UNITYEVENTS
ENABLE_VR
ENABLE_WEBCAM
ENABLE_UNITYWEBREQUEST
ENABLE_WWW
ENABLE_CLOUD_SERVICES
ENABLE_CLOUD_SERVICES_COLLAB
ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
ENABLE_CLOUD_SERVICES_ADS
ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
ENABLE_CLOUD_SERVICES_CRASH_REPORTING
ENABLE_CLOUD_SERVICES_PURCHASING
ENABLE_CLOUD_SERVICES_ANALYTICS
ENABLE_CLOUD_SERVICES_UNET
ENABLE_CLOUD_SERVICES_BUILD
ENABLE_CLOUD_LICENSE
ENABLE_EDITOR_HUB_LICENSE
ENABLE_WEBSOCKET_CLIENT
ENABLE_DIRECTOR_AUDIO
ENABLE_DIRECTOR_TEXTURE
ENABLE_MANAGED_JOBS
ENABLE_MANAGED_TRANSFORM_JOBS
ENABLE_MANAGED_ANIMATION_JOBS
ENABLE_MANAGED_AUDIO_JOBS
ENABLE_MANAGED_UNITYTLS
INCLUDE_DYNAMIC_GI
ENABLE_MONO_BDWGC
ENABLE_SCRIPTING_GC_WBARRIERS
PLATFORM_SUPPORTS_MONO
RENDER_SOFTWARE_CURSOR
ENABLE_VIDEO
PLATFORM_STANDALONE
PLATFORM_STANDALONE_WIN
UNITY_STANDALONE_WIN
UNITY_STANDALONE
UNITY_UGP_API
ENABLE_RUNTIME_GI
ENABLE_MOVIES
ENABLE_NETWORK
ENABLE_CRUNCH_TEXTURE_COMPRESSION
ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
ENABLE_CLUSTER_SYNC
ENABLE_CLUSTERINPUT
PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
ENABLE_WEBSOCKET_HOST
ENABLE_MONO
NET_4_6
ENABLE_PROFILER
DEBUG
TRACE
UNITY_ASSERTIONS
UNITY_EDITOR
UNITY_EDITOR_IG
UNITY_EDITOR_64
UNITY_EDITOR_WIN
ENABLE_CLOUD_FEATURES
ENABLE_UNITY_COLLECTIONS_CHECKS
ENABLE_BURST_AOT
UNITY_TEAM_LICENSE
ENABLE_CUSTOM_RENDER_TEXTURE
ENABLE_DIRECTOR
ENABLE_LOCALIZATION
ENABLE_SPRITES
ENABLE_TERRAIN
ENABLE_TILEMAP
ENABLE_TIMELINE
ENABLE_LEGACY_INPUT_MANAGER
0
2235264767632
941587210
D:/Unity/司导/SIP2.0/Assets/Scripts/SIPClientSceneSetup.cs
61
12
TMP_FontAsset
106
UNITY_2020_3_48
UNITY_2020_3
UNITY_2020
UNITY_5_3_OR_NEWER
UNITY_5_4_OR_NEWER
UNITY_5_5_OR_NEWER
UNITY_5_6_OR_NEWER
UNITY_2017_1_OR_NEWER
UNITY_2017_2_OR_NEWER
UNITY_2017_3_OR_NEWER
UNITY_2017_4_OR_NEWER
UNITY_2018_1_OR_NEWER
UNITY_2018_2_OR_NEWER
UNITY_2018_3_OR_NEWER
UNITY_2018_4_OR_NEWER
UNITY_2019_1_OR_NEWER
UNITY_2019_2_OR_NEWER
UNITY_2019_3_OR_NEWER
UNITY_2019_4_OR_NEWER
UNITY_2020_1_OR_NEWER
UNITY_2020_2_OR_NEWER
UNITY_2020_3_OR_NEWER
PLATFORM_ARCH_64
UNITY_64
UNITY_INCLUDE_TESTS
ENABLE_AR
ENABLE_AUDIO
ENABLE_CACHING
ENABLE_CLOTH
ENABLE_EVENT_QUEUE
ENABLE_MICROPHONE
ENABLE_MULTIPLE_DISPLAYS
ENABLE_PHYSICS
ENABLE_TEXTURE_STREAMING
ENABLE_VIRTUALTEXTURING
ENABLE_UNET
ENABLE_LZMA
ENABLE_UNITYEVENTS
ENABLE_VR
ENABLE_WEBCAM
ENABLE_UNITYWEBREQUEST
ENABLE_WWW
ENABLE_CLOUD_SERVICES
ENABLE_CLOUD_SERVICES_COLLAB
ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
ENABLE_CLOUD_SERVICES_ADS
ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
ENABLE_CLOUD_SERVICES_CRASH_REPORTING
ENABLE_CLOUD_SERVICES_PURCHASING
ENABLE_CLOUD_SERVICES_ANALYTICS
ENABLE_CLOUD_SERVICES_UNET
ENABLE_CLOUD_SERVICES_BUILD
ENABLE_CLOUD_LICENSE
ENABLE_EDITOR_HUB_LICENSE
ENABLE_WEBSOCKET_CLIENT
ENABLE_DIRECTOR_AUDIO
ENABLE_DIRECTOR_TEXTURE
ENABLE_MANAGED_JOBS
ENABLE_MANAGED_TRANSFORM_JOBS
ENABLE_MANAGED_ANIMATION_JOBS
ENABLE_MANAGED_AUDIO_JOBS
ENABLE_MANAGED_UNITYTLS
INCLUDE_DYNAMIC_GI
ENABLE_MONO_BDWGC
ENABLE_SCRIPTING_GC_WBARRIERS
PLATFORM_SUPPORTS_MONO
RENDER_SOFTWARE_CURSOR
ENABLE_VIDEO
PLATFORM_STANDALONE
PLATFORM_STANDALONE_WIN
UNITY_STANDALONE_WIN
UNITY_STANDALONE
UNITY_UGP_API
ENABLE_RUNTIME_GI
ENABLE_MOVIES
ENABLE_NETWORK
ENABLE_CRUNCH_TEXTURE_COMPRESSION
ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
ENABLE_CLUSTER_SYNC
ENABLE_CLUSTERINPUT
PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
ENABLE_WEBSOCKET_HOST
ENABLE_MONO
NET_4_6
ENABLE_PROFILER
DEBUG
TRACE
UNITY_ASSERTIONS
UNITY_EDITOR
UNITY_EDITOR_IG
UNITY_EDITOR_64
UNITY_EDITOR_WIN
ENABLE_CLOUD_FEATURES
ENABLE_UNITY_COLLECTIONS_CHECKS
ENABLE_BURST_AOT
UNITY_TEAM_LICENSE
ENABLE_CUSTOM_RENDER_TEXTURE
ENABLE_DIRECTOR
ENABLE_LOCALIZATION
ENABLE_SPRITES
ENABLE_TERRAIN
ENABLE_TILEMAP
ENABLE_TIMELINE
ENABLE_LEGACY_INPUT_MANAGER
0
2235264767848
941587210
D:/Unity/司导/SIP2.0/Assets/Scripts/SIPClientSceneSetup.cs
72
13
TMP_InputField
106
UNITY_2020_3_48
UNITY_2020_3
UNITY_2020
UNITY_5_3_OR_NEWER
UNITY_5_4_OR_NEWER
UNITY_5_5_OR_NEWER
UNITY_5_6_OR_NEWER
UNITY_2017_1_OR_NEWER
UNITY_2017_2_OR_NEWER
UNITY_2017_3_OR_NEWER
UNITY_2017_4_OR_NEWER
UNITY_2018_1_OR_NEWER
UNITY_2018_2_OR_NEWER
UNITY_2018_3_OR_NEWER
UNITY_2018_4_OR_NEWER
UNITY_2019_1_OR_NEWER
UNITY_2019_2_OR_NEWER
UNITY_2019_3_OR_NEWER
UNITY_2019_4_OR_NEWER
UNITY_2020_1_OR_NEWER
UNITY_2020_2_OR_NEWER
UNITY_2020_3_OR_NEWER
PLATFORM_ARCH_64
UNITY_64
UNITY_INCLUDE_TESTS
ENABLE_AR
ENABLE_AUDIO
ENABLE_CACHING
ENABLE_CLOTH
ENABLE_EVENT_QUEUE
ENABLE_MICROPHONE
ENABLE_MULTIPLE_DISPLAYS
ENABLE_PHYSICS
ENABLE_TEXTURE_STREAMING
ENABLE_VIRTUALTEXTURING
ENABLE_UNET
ENABLE_LZMA
ENABLE_UNITYEVENTS
ENABLE_VR
ENABLE_WEBCAM
ENABLE_UNITYWEBREQUEST
ENABLE_WWW
ENABLE_CLOUD_SERVICES
ENABLE_CLOUD_SERVICES_COLLAB
ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
ENABLE_CLOUD_SERVICES_ADS
ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
ENABLE_CLOUD_SERVICES_CRASH_REPORTING
ENABLE_CLOUD_SERVICES_PURCHASING
ENABLE_CLOUD_SERVICES_ANALYTICS
ENABLE_CLOUD_SERVICES_UNET
ENABLE_CLOUD_SERVICES_BUILD
ENABLE_CLOUD_LICENSE
ENABLE_EDITOR_HUB_LICENSE
ENABLE_WEBSOCKET_CLIENT
ENABLE_DIRECTOR_AUDIO
ENABLE_DIRECTOR_TEXTURE
ENABLE_MANAGED_JOBS
ENABLE_MANAGED_TRANSFORM_JOBS
ENABLE_MANAGED_ANIMATION_JOBS
ENABLE_MANAGED_AUDIO_JOBS
ENABLE_MANAGED_UNITYTLS
INCLUDE_DYNAMIC_GI
ENABLE_MONO_BDWGC
ENABLE_SCRIPTING_GC_WBARRIERS
PLATFORM_SUPPORTS_MONO
RENDER_SOFTWARE_CURSOR
ENABLE_VIDEO
PLATFORM_STANDALONE
PLATFORM_STANDALONE_WIN
UNITY_STANDALONE_WIN
UNITY_STANDALONE
UNITY_UGP_API
ENABLE_RUNTIME_GI
ENABLE_MOVIES
ENABLE_NETWORK
ENABLE_CRUNCH_TEXTURE_COMPRESSION
ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
ENABLE_CLUSTER_SYNC
ENABLE_CLUSTERINPUT
PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
ENABLE_WEBSOCKET_HOST
ENABLE_MONO
NET_4_6
ENABLE_PROFILER
DEBUG
TRACE
UNITY_ASSERTIONS
UNITY_EDITOR
UNITY_EDITOR_IG
UNITY_EDITOR_64
UNITY_EDITOR_WIN
ENABLE_CLOUD_FEATURES
ENABLE_UNITY_COLLECTIONS_CHECKS
ENABLE_BURST_AOT
UNITY_TEAM_LICENSE
ENABLE_CUSTOM_RENDER_TEXTURE
ENABLE_DIRECTOR
ENABLE_LOCALIZATION
ENABLE_SPRITES
ENABLE_TERRAIN
ENABLE_TILEMAP
ENABLE_TIMELINE
ENABLE_LEGACY_INPUT_MANAGER
0
2235264768064
941587210
D:/Unity/司导/SIP2.0/Assets/Scripts/SIPClientSceneSetup.cs
73
13
TextMeshProUGUI
106
UNITY_2020_3_48
UNITY_2020_3
UNITY_2020
UNITY_5_3_OR_NEWER
UNITY_5_4_OR_NEWER
UNITY_5_5_OR_NEWER
UNITY_5_6_OR_NEWER
UNITY_2017_1_OR_NEWER
UNITY_2017_2_OR_NEWER
UNITY_2017_3_OR_NEWER
UNITY_2017_4_OR_NEWER
UNITY_2018_1_OR_NEWER
UNITY_2018_2_OR_NEWER
UNITY_2018_3_OR_NEWER
UNITY_2018_4_OR_NEWER
UNITY_2019_1_OR_NEWER
UNITY_2019_2_OR_NEWER
UNITY_2019_3_OR_NEWER
UNITY_2019_4_OR_NEWER
UNITY_2020_1_OR_NEWER
UNITY_2020_2_OR_NEWER
UNITY_2020_3_OR_NEWER
PLATFORM_ARCH_64
UNITY_64
UNITY_INCLUDE_TESTS
ENABLE_AR
ENABLE_AUDIO
ENABLE_CACHING
ENABLE_CLOTH
ENABLE_EVENT_QUEUE
ENABLE_MICROPHONE
ENABLE_MULTIPLE_DISPLAYS
ENABLE_PHYSICS
ENABLE_TEXTURE_STREAMING
ENABLE_VIRTUALTEXTURING
ENABLE_UNET
ENABLE_LZMA
ENABLE_UNITYEVENTS
ENABLE_VR
ENABLE_WEBCAM
ENABLE_UNITYWEBREQUEST
ENABLE_WWW
ENABLE_CLOUD_SERVICES
ENABLE_CLOUD_SERVICES_COLLAB
ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
ENABLE_CLOUD_SERVICES_ADS
ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
ENABLE_CLOUD_SERVICES_CRASH_REPORTING
ENABLE_CLOUD_SERVICES_PURCHASING
ENABLE_CLOUD_SERVICES_ANALYTICS
ENABLE_CLOUD_SERVICES_UNET
ENABLE_CLOUD_SERVICES_BUILD
ENABLE_CLOUD_LICENSE
ENABLE_EDITOR_HUB_LICENSE
ENABLE_WEBSOCKET_CLIENT
ENABLE_DIRECTOR_AUDIO
ENABLE_DIRECTOR_TEXTURE
ENABLE_MANAGED_JOBS
ENABLE_MANAGED_TRANSFORM_JOBS
ENABLE_MANAGED_ANIMATION_JOBS
ENABLE_MANAGED_AUDIO_JOBS
ENABLE_MANAGED_UNITYTLS
INCLUDE_DYNAMIC_GI
ENABLE_MONO_BDWGC
ENABLE_SCRIPTING_GC_WBARRIERS
PLATFORM_SUPPORTS_MONO
RENDER_SOFTWARE_CURSOR
ENABLE_VIDEO
PLATFORM_STANDALONE
PLATFORM_STANDALONE_WIN
UNITY_STANDALONE_WIN
UNITY_STANDALONE
UNITY_UGP_API
ENABLE_RUNTIME_GI
ENABLE_MOVIES
ENABLE_NETWORK
ENABLE_CRUNCH_TEXTURE_COMPRESSION
ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
ENABLE_CLUSTER_SYNC
ENABLE_CLUSTERINPUT
PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
ENABLE_WEBSOCKET_HOST
ENABLE_MONO
NET_4_6
ENABLE_PROFILER
DEBUG
TRACE
UNITY_ASSERTIONS
UNITY_EDITOR
UNITY_EDITOR_IG
UNITY_EDITOR_64
UNITY_EDITOR_WIN
ENABLE_CLOUD_FEATURES
ENABLE_UNITY_COLLECTIONS_CHECKS
ENABLE_BURST_AOT
UNITY_TEAM_LICENSE
ENABLE_CUSTOM_RENDER_TEXTURE
ENABLE_DIRECTOR
ENABLE_LOCALIZATION
ENABLE_SPRITES
ENABLE_TERRAIN
ENABLE_TILEMAP
ENABLE_TIMELINE
ENABLE_LEGACY_INPUT_MANAGER
0

/target:library
/out:Temp/Unity.Timeline.Editor.dll
/nowarn:0169
/nowarn:0649
/refout:Temp/Unity.Timeline.Editor.dll.ref
/deterministic
/debug:portable
/optimize+
/nostdlib+
/preferreduilang:en-US
/langversion:8.0
/reference:Library/ScriptAssemblies/Unity.Timeline.dll
/reference:Library/ScriptAssemblies/UnityEditor.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEditor.UI.dll
/reference:Library/ScriptAssemblies/UnityEngine.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEngine.UI.dll
/reference:Assets/NuGet/Editor/NuGetForUnity.PluginAPI.dll
/reference:Assets/NuGet/Editor/NugetForUnity.dll
/reference:Assets/Packages/Concentus.2.2.2/lib/netstandard2.0/Concentus.dll
/reference:Assets/Packages/DirectShowLib.Standard.2.1.0/lib/netstandard2.0/DirectShowLib.dll
/reference:Assets/Packages/DnsClient.1.8.0/lib/netstandard2.0/DnsClient.dll
/reference:Assets/Packages/FFmpeg.AutoGen.7.0.0/lib/netstandard2.0/FFmpeg.AutoGen.dll
/reference:Assets/Packages/Microsoft.Bcl.AsyncInterfaces.9.0.0/lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll
/reference:Assets/Packages/Microsoft.Extensions.DependencyInjection.Abstractions.9.0.0/lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll
/reference:Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.0/lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll
/reference:Assets/Packages/Microsoft.Win32.Registry.5.0.0/lib/netstandard2.0/Microsoft.Win32.Registry.dll
/reference:Assets/Packages/Portable.BouncyCastle.1.9.0/lib/netstandard2.0/BouncyCastle.Crypto.dll
/reference:Assets/Packages/SIPSorcery.8.0.14/lib/netstandard2.0/SIPSorcery.dll
/reference:Assets/Packages/SIPSorcery.WebSocketSharp.0.0.1/lib/netstandard2.0/websocket-sharp.dll
/reference:Assets/Packages/SIPSorceryMedia.Abstractions.8.0.10/lib/netstandard2.0/SIPSorceryMedia.Abstractions.dll
/reference:Assets/Packages/SIPSorceryMedia.FFmpeg.8.0.10/lib/netstandard2.0/SIPSorceryMedia.FFmpeg.dll
/reference:Assets/Packages/System.Buffers.4.5.1/lib/netstandard2.0/System.Buffers.dll
/reference:Assets/Packages/System.Diagnostics.DiagnosticSource.9.0.0/lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll
/reference:Assets/Packages/System.Memory.4.5.5/lib/netstandard2.0/System.Memory.dll
/reference:Assets/Packages/System.Runtime.CompilerServices.Unsafe.6.0.0/lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll
/reference:Assets/Packages/System.Security.AccessControl.5.0.0/lib/netstandard2.0/System.Security.AccessControl.dll
/reference:Assets/Packages/System.Security.Principal.Windows.5.0.0/lib/netstandard2.0/System.Security.Principal.Windows.dll
/reference:Assets/Packages/System.Threading.Tasks.Extensions.4.5.4/lib/netstandard2.0/System.Threading.Tasks.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEditor.Graphs.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
/reference:"D:/Unity/司导/SIP2.0/Library/PackageCache/com.unity.collab-proxy@2.0.4/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll"
/reference:"D:/Unity/司导/SIP2.0/Library/PackageCache/com.unity.collab-proxy@2.0.4/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll"
/reference:"D:/Unity/司导/SIP2.0/Library/PackageCache/com.unity.collab-proxy@2.0.4/Lib/Editor/PlasticSCM/log4netPlastic.dll"
/reference:"D:/Unity/司导/SIP2.0/Library/PackageCache/com.unity.collab-proxy@2.0.4/Lib/Editor/PlasticSCM/unityplastic.dll"
/reference:"D:/Unity/司导/SIP2.0/Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll"
/reference:"D:/Unity/司导/SIP2.0/Library/PackageCache/com.unity.nuget.newtonsoft-json@3.0.2/Runtime/Newtonsoft.Json.dll"
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AutoStreamingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CloudFoundationModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIWidgetsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.IO.Compression.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll
/define:CSHARP_7_3_OR_NEWER
/define:CSHARP_7_OR_LATER
/define:DEBUG
/define:ENABLE_AR
/define:ENABLE_AUDIO
/define:ENABLE_BURST_AOT
/define:ENABLE_CACHING
/define:ENABLE_CLOTH
/define:ENABLE_CLOUD_FEATURES
/define:ENABLE_CLOUD_LICENSE
/define:ENABLE_CLOUD_SERVICES
/define:ENABLE_CLOUD_SERVICES_ADS
/define:ENABLE_CLOUD_SERVICES_ANALYTICS
/define:ENABLE_CLOUD_SERVICES_BUILD
/define:ENABLE_CLOUD_SERVICES_COLLAB
/define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
/define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
/define:ENABLE_CLOUD_SERVICES_PURCHASING
/define:ENABLE_CLOUD_SERVICES_UNET
/define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
/define:ENABLE_CLUSTERINPUT
/define:ENABLE_CLUSTER_SYNC
/define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
/define:ENABLE_CUSTOM_RENDER_TEXTURE
/define:ENABLE_DIRECTOR
/define:ENABLE_DIRECTOR_AUDIO
/define:ENABLE_DIRECTOR_TEXTURE
/define:ENABLE_EDITOR_HUB_LICENSE
/define:ENABLE_EVENT_QUEUE
/define:ENABLE_LEGACY_INPUT_MANAGER
/define:ENABLE_LOCALIZATION
/define:ENABLE_LZMA
/define:ENABLE_MANAGED_ANIMATION_JOBS
/define:ENABLE_MANAGED_AUDIO_JOBS
/define:ENABLE_MANAGED_JOBS
/define:ENABLE_MANAGED_TRANSFORM_JOBS
/define:ENABLE_MANAGED_UNITYTLS
/define:ENABLE_MICROPHONE
/define:ENABLE_MONO
/define:ENABLE_MONO_BDWGC
/define:ENABLE_MOVIES
/define:ENABLE_MULTIPLE_DISPLAYS
/define:ENABLE_NETWORK
/define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
/define:ENABLE_PHYSICS
/define:ENABLE_PROFILER
/define:ENABLE_RUNTIME_GI
/define:ENABLE_SCRIPTING_GC_WBARRIERS
/define:ENABLE_SPRITES
/define:ENABLE_TERRAIN
/define:ENABLE_TEXTURE_STREAMING
/define:ENABLE_TILEMAP
/define:ENABLE_TIMELINE
/define:ENABLE_UNET
/define:ENABLE_UNITYEVENTS
/define:ENABLE_UNITYWEBREQUEST
/define:ENABLE_UNITY_COLLECTIONS_CHECKS
/define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
/define:ENABLE_VIDEO
/define:ENABLE_VIRTUALTEXTURING
/define:ENABLE_VR
/define:ENABLE_WEBCAM
/define:ENABLE_WEBSOCKET_CLIENT
/define:ENABLE_WEBSOCKET_HOST
/define:ENABLE_WWW
/define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
/define:INCLUDE_DYNAMIC_GI
/define:NET_4_6
/define:PLATFORM_ARCH_64
/define:PLATFORM_STANDALONE
/define:PLATFORM_STANDALONE_WIN
/define:PLATFORM_SUPPORTS_MONO
/define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
/define:RENDER_SOFTWARE_CURSOR
/define:TRACE
/define:UNITY_2017_1_OR_NEWER
/define:UNITY_2017_2_OR_NEWER
/define:UNITY_2017_3_OR_NEWER
/define:UNITY_2017_4_OR_NEWER
/define:UNITY_2018_1_OR_NEWER
/define:UNITY_2018_2_OR_NEWER
/define:UNITY_2018_3_OR_NEWER
/define:UNITY_2018_4_OR_NEWER
/define:UNITY_2019_1_OR_NEWER
/define:UNITY_2019_2_OR_NEWER
/define:UNITY_2019_3_OR_NEWER
/define:UNITY_2019_4_OR_NEWER
/define:UNITY_2020
/define:UNITY_2020_1_OR_NEWER
/define:UNITY_2020_2_OR_NEWER
/define:UNITY_2020_3
/define:UNITY_2020_3_48
/define:UNITY_2020_3_OR_NEWER
/define:UNITY_5_3_OR_NEWER
/define:UNITY_5_4_OR_NEWER
/define:UNITY_5_5_OR_NEWER
/define:UNITY_5_6_OR_NEWER
/define:UNITY_64
/define:UNITY_ASSERTIONS
/define:UNITY_EDITOR
/define:UNITY_EDITOR_64
/define:UNITY_EDITOR_IG
/define:UNITY_EDITOR_WIN
/define:UNITY_INCLUDE_TESTS
/define:UNITY_STANDALONE
/define:UNITY_STANDALONE_WIN
/define:UNITY_TEAM_LICENSE
/define:UNITY_UGP_API
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\ActionContext.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\ActionManager.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\ClipAction.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\ClipsActions.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\IAction.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\IMenuChecked.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\IMenuName.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\Invoker.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\MarkerAction.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\MarkerActions.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\Menus\MenuItemActionBase.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\Menus\TimelineContextMenu.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\TimelineAction.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\TimelineActions.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\TrackAction.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Actions\TrackActions.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Activation\ActivationTrackEditor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Activation\ActivationTrackInspector.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Analytics\TimelineAnalytics.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\AnimationClipActions.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\AnimationClipCurveCache.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\AnimationClipExtensions.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\AnimationOffsetMenu.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\AnimationPlayableAssetEditor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\AnimationTrackActions.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\BindingSelector.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\BindingTreeViewDataSource.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\BindingTreeViewDataSourceGUI.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\ClipCurveEditor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\CurveDataSource.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\CurveTreeViewNode.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\CurvesProxy.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Animation\TimelineAnimationUtilities.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Attributes\ActiveInModeAttribute.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Attributes\MenuEntryAttribute.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Attributes\ShortcutAttribute.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Attributes\TimelineShortcutAttribute.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Audio\AudioClipPropertiesDrawer.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Audio\AudioPlayableAssetEditor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Audio\AudioPlayableAssetInspector.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Audio\AudioTrackInspector.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\ControlTrack\ControlPlayableAssetEditor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\CurveEditUtility.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\CustomEditors\ClipEditor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\CustomEditors\CustomTimelineEditorCache.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\CustomEditors\MarkerEditor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\CustomEditors\MarkerTrackEditor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\CustomEditors\TrackEditor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\DirectorNamedColor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\DirectorStyles.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Extensions\AnimatedParameterExtensions.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Extensions\AnimationTrackExtensions.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Extensions\TrackExtensions.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Items\ClipItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Items\ITimelineItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Items\ItemsGroup.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Items\ItemsPerTrack.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Items\ItemsUtils.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Items\MarkerItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\AddDelete\AddDeleteItemModeMix.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\AddDelete\AddDeleteItemModeReplace.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\AddDelete\AddDeleteItemModeRipple.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\AddDelete\IAddDeleteItemMode.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Cursors\TimelineCursors.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\EditMode.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\EditModeInputHandler.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Move\IMoveItemMode.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Move\MoveItemHandler.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Move\MoveItemModeMix.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Move\MoveItemModeReplace.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Move\MoveItemModeRipple.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Move\MovingItems.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Sequence\EaseClip.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Sequence\Jog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Sequence\MarkerHeaderContextMenu.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Sequence\RectangleSelect.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Sequence\RectangleTool.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Sequence\RectangleZoom.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Sequence\SelectAndMoveItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Sequence\TrackZoom.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Sequence\TrimClip.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\TimeAreaAutoPanner.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\TimeIndicator.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\TimelineClipGroup.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Trim\ITrimItemMode.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Trim\TrimItemModeMix.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Trim\TrimItemModeReplace.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Trim\TrimItemModeRipple.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Utils\EditModeGUIUtils.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Utils\EditModeMixUtils.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Utils\EditModeReplaceUtils.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Utils\EditModeRippleUtils.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Utils\EditModeUtils.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Utils\ManipulatorsUtils.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Manipulators\Utils\PlacementValidity.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\MenuPriority.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Playables\ControlPlayableInspector.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Properties\AssemblyInfo.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Recording\AnimationTrackRecorder.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Recording\TimelineRecording.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Recording\TimelineRecordingContextualResponder.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Recording\TimelineRecording_Monobehaviour.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Recording\TimelineRecording_PlayableAsset.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Recording\TrackAssetRecordingExtensions.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Shortcuts.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\SignalAssetInspector.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\SignalEmitterEditor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\SignalEmitterInspector.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\SignalEventDrawer.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\SignalManager.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\SignalReceiverHeader.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\SignalReceiverInspector.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\SignalUtility.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\Styles.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\TreeView\SignalListFactory.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\TreeView\SignalReceiverItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Signals\TreeView\SignalReceiverTreeView.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\State\ISequenceState.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\State\SequenceHierarchy.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\State\SequencePath.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\State\SequenceState.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\State\WindowState.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\TimelineEditor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\TimelineHelpers.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\TimelineSelection.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\TimelineUtility.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Tooltip.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Trackhead.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Undo\ApplyDefaultUndoAttribute.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Undo\UndoExtensions.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Undo\UndoScope.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\UnityEditorInternals.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\AnimatedParameterCache.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\AnimatedParameterUtility.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\AnimatedPropertyUtility.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\BindingUtility.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\BreadcrumbDrawer.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\ClipModifier.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Clipboard.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\ControlPlayableUtility.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\CustomTrackDrawerAttribute.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\DisplayNameHelper.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Graphics.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\KeyTraverser.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\MarkerModifier.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\ObjectExtension.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\ObjectReferenceField.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\PropertyCollector.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Range.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Scopes\GUIColorOverride.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Scopes\GUIGroupScope.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Scopes\GUIMixedValueScope.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Scopes\GUIViewportScope.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Scopes\HorizontalScope.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Scopes\IndentLevelScope.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Scopes\LabelWidthScope.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\Scopes\PropertyScope.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\SequenceSelectorNameFormater.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\SpacePartitioner.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\StyleManager.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\StyleNormalColorOverride.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\TimeReferenceUtility.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\TimelineKeyboardNavigation.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\TrackModifier.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\TrackResourceCache.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Utilities\TypeUtility.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\Modes\TimeReferenceMode.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\Modes\TimelineActiveMode.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\Modes\TimelineAssetEditionMode.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\Modes\TimelineDisabledMode.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\Modes\TimelineInactiveMode.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\Modes\TimelineMode.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\Modes\TimelineReadOnlyMode.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\PlaybackScroller.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineMarkerHeaderGUI.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindowTimeControl.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_ActiveTimeline.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_Breadcrumbs.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_Duration.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_EditorCallbacks.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_Gui.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_HeaderGui.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_Manipulators.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_PlayRange.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_PlayableLookup.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_PreviewPlayMode.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_Selection.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_StateChange.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_TimeArea.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_TimeCursor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\TimelineWindow_TrackGui.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\ViewModel\ScriptableObjectViewPrefs.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\ViewModel\TimelineAssetViewModel.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\ViewModel\TimelineWindowViewPrefs.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\Window\WindowConstants.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\AnimationPlayableAssetInspector.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\AnimationTrackInspector.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\BasicAssetInspector.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\BuiltInCurvePresets.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\ClipInspector\ClipInspector.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\ClipInspector\ClipInspectorCurveEditor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\ClipInspector\ClipInspectorSelectionInfo.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\CurvesOwner\CurvesOwnerInspectorHelper.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\CurvesOwner\ICurvesOwnerInspectorWrapper.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\DirectorNamedColorInspector.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\EditorClip.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\EditorClipFactory.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\GroupTrackInspector.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\MarkerInspector.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\TimeFieldDrawer.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\TimelineAssetInspector.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\TimelineInspectorUtility.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\TimelinePreferences.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\TimelineProjectSettings.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\inspectors\TrackAssetInspector.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\AnimationTrackKeyDataSource.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Control.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Drawers\AnimationTrackDrawer.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Drawers\ClipDrawer.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Drawers\InfiniteTrackDrawer.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Drawers\Layers\ClipsLayer.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Drawers\Layers\ItemsLayer.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Drawers\Layers\MarkersLayer.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Drawers\TrackDrawer.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Drawers\TrackItemsDrawer.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\IPropertyKeyDataSource.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\IRowGUI.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\ItemGui\ISelectable.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\ItemGui\TimelineClipGUI.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\ItemGui\TimelineItemGUI.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\ItemGui\TimelineMarkerClusterGUI.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\ItemGui\TimelineMarkerGUI.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\ManipulationsClips.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\ManipulationsTimeline.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\ManipulationsTracks.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Manipulator.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\PickerUtils.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Snapping\IAttractable.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Snapping\ISnappable.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\Snapping\SnapEngine.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TimelineClipHandle.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TimelineClipUnion.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TimelineDataSource.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TimelineDragging.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TimelineTreeView.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TimelineTreeViewGUI.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TrackGui\InlineCurveEditor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TrackGui\TimelineGroupGUI.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TrackGui\TimelineTrackBaseGUI.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TrackGui\TimelineTrackErrorGUI.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TrackGui\TimelineTrackGUI.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TrackGui\TrackResizeHandle.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8\Editor\treeview\TrackPropertyCurvesDataSource.cs"

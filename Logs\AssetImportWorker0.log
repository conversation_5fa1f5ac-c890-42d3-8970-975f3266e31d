Using pre-set license
Built from '2020.3/china_unity/release' branch; Version is '2020.3.48f1c1 (06fbdfbf16e3) revision 457695'; Using compiler version '192528614'; Build Type 'Release'
OS: 'Windows 11  (10.0.26200) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 16167 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0

COMMAND LINE ARGUMENTS:
D:\Unity\2020.3.48f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/Unity/司导/SIP2.0
-logFile
Logs/AssetImportWorker0.log
-srvPort
1923
Successfully changed project path to: D:/Unity/司导/SIP2.0
D:/Unity/司导/SIP2.0
Using Asset Import Pipeline V2.
Player connection [23244] Host "[IP] ************* [Port] ********** [Flags] 2 [Guid] ********** [EditorId] ********** [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-4TNQCCV1) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [23244] Host "[IP] ************* [Port] ********** [Flags] 2 [Guid] ********** [EditorId] ********** [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-4TNQCCV1) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.Refreshing native plugins compatible for Editor in 160.63 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2020.3.48f1c1 (06fbdfbf16e3)
[Subsystems] Discovering subsystems at path D:/Unity/2020.3.48f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Unity/司导/SIP2.0/Assets
GfxDevice: creating device client; threaded=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: Intel(R) Iris(R) Xe Graphics (ID=0x9a49)
    Vendor:   
    VRAM:     8083 MB
    Driver:   32.0.101.5990
Initialize mono
Mono path[0] = 'D:/Unity/2020.3.48f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit'
Mono config path = 'D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56580
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
Registered in 0.006822 seconds.
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 119.40 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.893 seconds
Domain Reload Profiling:
	ReloadAssembly (1894ms)
		BeginReloadAssembly (369ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (3ms)
		EndReloadAssembly (1316ms)
			LoadAssemblies (371ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (454ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (80ms)
			SetupLoadedEditorAssemblies (477ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (15ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (120ms)
				BeforeProcessingInitializeOnLoad (36ms)
				ProcessInitializeOnLoadAttributes (229ms)
				ProcessInitializeOnLoadMethodAttributes (76ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.141509 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.67 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  3.680 seconds
Domain Reload Profiling:
	ReloadAssembly (3682ms)
		BeginReloadAssembly (478ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (13ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (49ms)
		EndReloadAssembly (3073ms)
			LoadAssemblies (460ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (951ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (107ms)
			SetupLoadedEditorAssemblies (1513ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (187ms)
				ProcessInitializeOnLoadAttributes (1288ms)
				ProcessInitializeOnLoadMethodAttributes (17ms)
				AfterProcessingInitializeOnLoad (5ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (4ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.20 seconds
Refreshing native plugins compatible for Editor in 1.53 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1489 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 72.1 MB.
System memory in use after: 72.2 MB.

Unloading 108 unused Assets to reduce memory usage. Loaded Objects now: 1839.
Total: 8.620900 ms (FindLiveObjects: 0.281500 ms CreateObjectMapping: 0.181800 ms MarkObjects: 7.897000 ms  DeleteObjects: 0.257600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  path: Assets/Scripts/Scripts.asmdef
  artifactKey: Guid(203870d635fecc4488d9131e02860433) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Scripts.asmdef using Guid(203870d635fecc4488d9131e02860433) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c301801b4b7a84e5679274345538ac30') in 0.211709 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.001023 seconds.
  path: Assets/Scenes/SIPClient2.unity
  artifactKey: Guid(82546fb0c8503f241bb2c0d00d6580fc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/SIPClient2.unity using Guid(82546fb0c8503f241bb2c0d00d6580fc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '873f54b16da87bd78c43428cd057f4fd') in 0.024570 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.001293 seconds.
  path: Assets/Scenes/SIPClient1.unity
  artifactKey: Guid(9fc0d4010bbf28b4594072e72b8655ab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/SIPClient1.unity using Guid(9fc0d4010bbf28b4594072e72b8655ab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8e83dc706e5ffe9ffb910d00d765cfe4') in 0.047176 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000878 seconds.
  path: Assets/13208266.wav
  artifactKey: Guid(70fcd435bf571364a855c944c7ddc8f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/13208266.wav using Guid(70fcd435bf571364a855c944c7ddc8f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5449efa8223c3ee40918b446fd66459a') in 0.459249 seconds 

m_ProjectFiles:
  m_ManifestFileStatus:
    m_FilePath: "D:/Unity/\u53F8\u5BFC/SIP2.0/Packages/manifest.json"
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638882284712841202
    m_Hash: 2554735319
  m_LockFileStatus:
    m_FilePath: "D:/Unity/\u53F8\u5BFC/SIP2.0/Packages/packages-lock.json"
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638882284713562552
    m_Hash: 1470091307
m_EmbeddedPackageManifests:
  m_ManifestsStatus: {}
m_PackageAssets:
  m_Packages:
    m_ResolvedPackages:
    - packageId: "com.justinpbarnett.unity-mcp@file:C:\\Users\\<USER>\\OneDrive\\\u6587\u6863\\Cline\\MCP\\unity-mcp\\UnityMcpBridge"
      testable: 0
      isDirectDependency: 1
      version: 2.0.0
      source: 4
      resolvedPath: "C:\\Users\\<USER>\\OneDrive\\\u6587\u6863\\Cline\\MCP\\unity-mcp\\UnityMcpBridge"
      assetPath: Packages/com.justinpbarnett.unity-mcp
      name: com.justinpbarnett.unity-mcp
      displayName: Unity MCP Bridge
      author:
        name: 
        email: 
        url: 
      category: 
      type: 
      description: A bridge that manages and communicates with the sister application,
        Unity MCP Server, which allows for communications with MCP Clients like Claude
        Desktop or Cursor.
      status: 0
      errors: []
      versions:
        all: []
        compatible: []
        verified: 
      dependencies:
      - name: com.unity.nuget.newtonsoft-json
        version: 3.0.2
      resolvedDependencies:
      - name: com.unity.nuget.newtonsoft-json
        version: 3.0.2
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.collab-proxy@2.0.4
      testable: 0
      isDirectDependency: 1
      version: 2.0.4
      source: 1
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.collab-proxy@2.0.4"
      assetPath: Packages/com.unity.collab-proxy
      name: com.unity.collab-proxy
      displayName: Version Control
      author:
        name: 
        email: 
        url: 
      category: Editor
      type: 
      description: The package gives you the ability to use Unity Version Control
        in the Unity editor. To use Unity Version Control, a subscription is required.
        Learn more about how you can get started for free by visiting plasticscm.com.
      status: 0
      errors: []
      versions:
        all:
        - 1.2.3-preview
        - 1.2.4-preview
        - 1.2.6
        - 1.2.7
        - 1.2.9
        - 1.2.11
        - 1.2.15
        - 1.2.16
        - 1.2.17-preview.3
        - 1.3.2
        - 1.3.3
        - 1.3.4
        - 1.3.5
        - 1.3.6
        - 1.3.7
        - 1.3.8
        - 1.3.9
        - 1.4.9
        - 1.5.7
        - 1.6.0
        - 1.7.1
        - 1.8.0
        - 1.9.0
        - 1.10.2
        - 1.11.2
        - 1.12.5
        - 1.13.5
        - 1.14.1
        - 1.14.4
        - 1.14.7
        - 1.14.9
        - 1.14.12
        - 1.14.13
        - 1.14.15
        - 1.14.16
        - 1.14.17
        - 1.14.18
        - 1.15.1
        - 1.15.4
        - 1.15.7
        - 1.15.9
        - 1.15.12
        - 1.15.13
        - 1.15.15
        - 1.15.16
        - 1.15.17
        - 1.15.18
        - 1.17.0
        - 1.17.1
        - 1.17.2
        - 1.17.6
        - 1.17.7
        - 2.0.0-preview.6
        - 2.0.0-preview.8
        - 2.0.0-preview.15
        - 2.0.0-preview.17
        - 2.0.0-preview.20
        - 2.0.0-preview.21
        - 2.0.0-preview.22
        - 2.0.0
        - 2.0.1
        - 2.0.3
        - 2.0.4
        - 2.0.5
        - 2.0.7
        - 2.1.0-preview.3
        - 2.1.0-preview.5
        - 2.1.0-preview.6
        - 2.1.0
        - 2.2.0
        - 2.3.1
        - 2.4.3
        - 2.4.4
        - 2.5.1
        - 2.5.2
        - 2.6.0
        - 2.7.1
        - 2.8.1
        - 2.8.2
        compatible:
        - 2.0.4
        - 2.0.5
        - 2.0.7
        - 2.1.0-preview.3
        - 2.1.0-preview.5
        - 2.1.0-preview.6
        - 2.1.0
        - 2.2.0
        - 2.3.1
        - 2.4.3
        - 2.4.4
        - 2.5.1
        - 2.5.2
        verified: 2.0.7
      dependencies: []
      resolvedDependencies: []
      keywords:
      - backup
      - cloud
      - collab
      - collaborate
      - collaboration
      - control
      - devops
      - plastic
      - plasticscm
      - source
      - team
      - teams
      - version
      - vcs
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638176024310000000
      documentationUrl: https://docs.unity3d.com/Packages/com.unity.collab-proxy@2.0/manual/index.html
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/com.unity.cloud.collaborate.git
        revision: dc15ef2ed609e8c19d0525a46c5bf7dd78df8432
        path: 
    - packageId: com.unity.ide.rider@3.0.21
      testable: 0
      isDirectDependency: 1
      version: 3.0.21
      source: 1
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.ide.rider@3.0.21"
      assetPath: Packages/com.unity.ide.rider
      name: com.unity.ide.rider
      displayName: JetBrains Rider Editor
      author:
        name: 
        email: 
        url: 
      category: 
      type: 
      description: The JetBrains Rider Editor package provides an integration for
        using the JetBrains Rider IDE as a code editor for Unity. It adds support
        for generating .csproj files for code completion and auto-discovery of installations.
      status: 0
      errors: []
      versions:
        all:
        - 1.0.2
        - 1.0.6
        - 1.0.8
        - 1.1.0
        - 1.1.1
        - 1.1.2-preview
        - 1.1.2-preview.2
        - 1.1.3-preview.1
        - 1.1.4-preview
        - 1.1.4
        - 1.2.0-preview
        - 1.2.1
        - 2.0.0-preview
        - 2.0.1
        - 2.0.2
        - 2.0.3
        - 2.0.5
        - 2.0.7
        - 3.0.1
        - 3.0.2
        - 3.0.3
        - 3.0.4
        - 3.0.5
        - 3.0.6
        - 3.0.7
        - 3.0.9
        - 3.0.10
        - 3.0.12
        - 3.0.13
        - 3.0.14
        - 3.0.15
        - 3.0.16
        - 3.0.17
        - 3.0.18
        - 3.0.20
        - 3.0.21
        - 3.0.22
        - 3.0.24
        - 3.0.25
        - 3.0.26
        - 3.0.27
        - 3.0.28
        - 3.0.31
        - 3.0.34
        - 3.0.35
        - 3.0.36
        compatible:
        - 1.0.2
        - 1.0.6
        - 1.0.8
        - 1.1.0
        - 1.1.1
        - 1.1.2-preview
        - 1.1.2-preview.2
        - 1.1.3-preview.1
        - 1.1.4-preview
        - 1.1.4
        - 1.2.0-preview
        - 1.2.1
        - 2.0.0-preview
        - 2.0.1
        - 2.0.2
        - 2.0.3
        - 2.0.5
        - 2.0.7
        - 3.0.1
        - 3.0.2
        - 3.0.3
        - 3.0.4
        - 3.0.5
        - 3.0.6
        - 3.0.7
        - 3.0.9
        - 3.0.10
        - 3.0.12
        - 3.0.13
        - 3.0.14
        - 3.0.15
        - 3.0.16
        - 3.0.17
        - 3.0.18
        - 3.0.20
        - 3.0.21
        - 3.0.22
        - 3.0.24
        - 3.0.25
        - 3.0.26
        - 3.0.27
        - 3.0.28
        - 3.0.31
        - 3.0.34
        - 3.0.35
        - 3.0.36
        verified: 3.0.36
      dependencies:
      - name: com.unity.ext.nunit
        version: 1.0.6
      resolvedDependencies:
      - name: com.unity.ext.nunit
        version: 1.0.6
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638175749180000000
      documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.rider@3.0/manual/index.html
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.rider.git
        revision: bb7732dcafe867368246b7661dda05368b74dd8e
        path: 
    - packageId: com.unity.ide.visualstudio@2.0.23
      testable: 0
      isDirectDependency: 1
      version: 2.0.23
      source: 1
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.ide.visualstudio@2.0.23"
      assetPath: Packages/com.unity.ide.visualstudio
      name: com.unity.ide.visualstudio
      displayName: Visual Studio Editor
      author:
        name: 
        email: 
        url: 
      category: 
      type: 
      description: Code editor integration for supporting Visual Studio as code editor
        for unity. Adds support for generating csproj files for intellisense purposes,
        auto discovery of installations, etc.
      status: 0
      errors: []
      versions:
        all:
        - 1.0.2
        - 1.0.3
        - 1.0.4
        - 1.0.9
        - 1.0.10
        - 1.0.11
        - 2.0.0
        - 2.0.1
        - 2.0.2
        - 2.0.3
        - 2.0.5
        - 2.0.7
        - 2.0.8
        - 2.0.9
        - 2.0.11
        - 2.0.12
        - 2.0.14
        - 2.0.15
        - 2.0.16
        - 2.0.17
        - 2.0.18
        - 2.0.20
        - 2.0.21
        - 2.0.22
        - 2.0.23
        compatible:
        - 1.0.2
        - 1.0.3
        - 1.0.4
        - 1.0.9
        - 1.0.10
        - 1.0.11
        - 2.0.0
        - 2.0.1
        - 2.0.2
        - 2.0.3
        - 2.0.5
        - 2.0.7
        - 2.0.8
        - 2.0.9
        - 2.0.11
        - 2.0.12
        - 2.0.14
        - 2.0.15
        - 2.0.16
        - 2.0.17
        - 2.0.18
        - 2.0.20
        - 2.0.21
        - 2.0.22
        - 2.0.23
        verified: 2.0.23
      dependencies:
      - name: com.unity.test-framework
        version: 1.1.9
      resolvedDependencies:
      - name: com.unity.test-framework
        version: 1.1.33
      - name: com.unity.ext.nunit
        version: 1.0.6
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638787282000000000
      documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.visualstudio@2.0/manual/index.html
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.visualstudio.git
        revision: 0fe3b29f9aff2b90b9f0962ae35036a824d3dd6b
        path: 
    - packageId: com.unity.ide.vscode@1.2.5
      testable: 0
      isDirectDependency: 1
      version: 1.2.5
      source: 1
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.ide.vscode@1.2.5"
      assetPath: Packages/com.unity.ide.vscode
      name: com.unity.ide.vscode
      displayName: Visual Studio Code Editor
      author:
        name: 
        email: 
        url: 
      category: 
      type: 
      description: Code editor integration for supporting Visual Studio Code as code
        editor for unity. Adds support for generating csproj files for intellisense
        purposes, auto discovery of installations, etc.
      status: 0
      errors: []
      versions:
        all:
        - 1.0.2
        - 1.0.3
        - 1.0.7
        - 1.1.0
        - 1.1.2
        - 1.1.3
        - 1.1.4
        - 1.2.0
        - 1.2.1
        - 1.2.2
        - 1.2.3
        - 1.2.4
        - 1.2.5
        compatible:
        - 1.0.2
        - 1.0.3
        - 1.0.7
        - 1.1.0
        - 1.1.2
        - 1.1.3
        - 1.1.4
        - 1.2.0
        - 1.2.1
        - 1.2.2
        - 1.2.3
        - 1.2.4
        - 1.2.5
        verified: 1.2.5
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 637800255360000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.com/Unity-Technologies/com.unity.ide.vscode.git
        revision: b0740c80bfc2440527c317109f7c3d9100132722
        path: 
    - packageId: com.unity.test-framework@1.1.33
      testable: 0
      isDirectDependency: 1
      version: 1.1.33
      source: 1
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.test-framework@1.1.33"
      assetPath: Packages/com.unity.test-framework
      name: com.unity.test-framework
      displayName: Test Framework
      author:
        name: 
        email: 
        url: 
      category: Unity Test Framework
      type: 
      description: Test framework for running Edit mode and Play mode tests in Unity.
      status: 0
      errors: []
      versions:
        all:
        - 0.0.4-preview
        - 0.0.29-preview
        - 1.0.0
        - 1.0.7
        - 1.0.9
        - 1.0.11
        - 1.0.12
        - 1.0.13
        - 1.0.14
        - 1.0.16
        - 1.0.17
        - 1.0.18
        - 1.1.0
        - 1.1.1
        - 1.1.2
        - 1.1.3
        - 1.1.5
        - 1.1.8
        - 1.1.9
        - 1.1.11
        - 1.1.13
        - 1.1.14
        - 1.1.16
        - 1.1.18
        - 1.1.19
        - 1.1.20
        - 1.1.22
        - 1.1.24
        - 1.1.26
        - 1.1.27
        - 1.1.29
        - 1.1.30
        - 1.1.31
        - 1.1.33
        - 1.3.0
        - 1.3.1
        - 1.3.2
        - 1.3.3
        - 1.3.4
        - 1.3.5
        - 1.3.7
        - 1.3.8
        - 1.3.9
        - 1.4.0
        - 1.4.1
        - 1.4.2
        - 1.4.3
        - 1.4.4
        - 1.4.5
        - 1.4.6
        - 2.0.1-exp.1
        - 2.0.1-exp.2
        - 2.0.1-pre.12
        - 2.0.1-pre.18
        compatible:
        - 1.1.33
        - 1.3.0
        - 1.3.1
        - 1.3.2
        - 1.3.3
        - 1.3.4
        - 1.3.5
        - 1.3.7
        - 1.3.8
        - 1.3.9
        - 1.4.0
        - 1.4.1
        - 1.4.2
        - 1.4.3
        - 1.4.4
        - 1.4.5
        - 1.4.6
        - 2.0.1-exp.1
        - 2.0.1-exp.2
        - 2.0.1-pre.12
        - 2.0.1-pre.18
        verified: 1.1.33
      dependencies:
      - name: com.unity.ext.nunit
        version: 1.0.6
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.ext.nunit
        version: 1.0.6
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      keywords:
      - Test
      - TestFramework
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 637938212700000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.com/Unity-Technologies/com.unity.test-framework.git
        revision: 34a4d423d64926635eb36d3bb86276179cc186e1
        path: 
    - packageId: com.unity.textmeshpro@3.0.9
      testable: 0
      isDirectDependency: 1
      version: 3.0.9
      source: 1
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.textmeshpro@3.0.9"
      assetPath: Packages/com.unity.textmeshpro
      name: com.unity.textmeshpro
      displayName: TextMeshPro
      author:
        name: 
        email: 
        url: 
      category: Text Rendering
      type: 
      description: 'TextMeshPro is the ultimate text solution for Unity. It''s the
        perfect replacement for Unity''s UI Text and the legacy Text Mesh.


        Powerful
        and easy to use, TextMeshPro (also known as TMP) uses Advanced Text Rendering
        techniques along with a set of custom shaders; delivering substantial visual
        quality improvements while giving users incredible flexibility when it comes
        to text styling and texturing.


        TextMeshPro provides Improved Control
        over text formatting and layout with features like character, word, line
        and paragraph spacing, kerning, justified text, Links, over 30 Rich Text
        Tags available, support for Multi Font & Sprites, Custom Styles and more.


        Great
        performance. Since the geometry created by TextMeshPro uses two triangles
        per character just like Unity''s text components, this improved visual quality
        and flexibility comes at no additional performance cost.'
      status: 0
      errors: []
      versions:
        all:
        - 0.1.2
        - 1.0.21
        - 1.0.23
        - 1.0.25
        - 1.0.26
        - 1.1.0
        - 1.2.0
        - 1.2.1
        - 1.2.2
        - 1.2.3
        - 1.2.4
        - 1.3.0-preview
        - 1.3.0
        - 1.4.0-preview.1b
        - 1.4.0-preview.2a
        - 1.4.0-preview.3a
        - 1.4.0
        - 1.4.1-preview.1
        - 1.4.1
        - 1.5.0-preview.1
        - 1.5.0-preview.2
        - 1.5.0-preview.3
        - 1.5.0-preview.4
        - 1.5.0-preview.5
        - 1.5.0-preview.6
        - 1.5.0-preview.7
        - 1.5.0-preview.8
        - 1.5.0-preview.10
        - 1.5.0-preview.11
        - 1.5.0-preview.12
        - 1.5.0-preview.13
        - 1.5.0-preview.14
        - 1.5.0
        - 1.5.1
        - 1.5.3
        - 1.5.4
        - 1.5.5
        - 1.5.6
        - 1.6.0-preview.1
        - 2.0.0
        - 2.0.1-preview.1
        - 2.0.1
        - 2.1.0-preview.1
        - 2.1.0-preview.2
        - 2.1.0-preview.3
        - 2.1.0-preview.4
        - 2.1.0-preview.5
        - 2.1.0-preview.7
        - 2.1.0-preview.8
        - 2.1.0-preview.10
        - 2.1.0-preview.11
        - 2.1.0-preview.12
        - 2.1.0-preview.13
        - 2.1.0-preview.14
        - 2.1.0
        - 2.1.1
        - 2.1.3
        - 2.1.4
        - 2.1.5
        - 2.1.6
        - 2.2.0-preview.1
        - 2.2.0-preview.2
        - 2.2.0-preview.3
        - 3.0.0-preview.1
        - 3.0.0-preview.3
        - 3.0.0-preview.4
        - 3.0.0-preview.5
        - 3.0.0-preview.7
        - 3.0.0-preview.8
        - 3.0.0-preview.10
        - 3.0.0-preview.11
        - 3.0.0-preview.12
        - 3.0.0-preview.13
        - 3.0.0-preview.14
        - 3.0.0
        - 3.0.1
        - 3.0.3
        - 3.0.4
        - 3.0.5
        - 3.0.6
        - 3.0.7
        - 3.0.8
        - 3.0.9
        - 3.2.0-pre.1
        - 3.2.0-pre.2
        - 3.2.0-pre.3
        - 3.2.0-pre.4
        - 3.2.0-pre.5
        - 3.2.0-pre.6
        - 3.2.0-pre.7
        - 3.2.0-pre.8
        - 3.2.0-pre.9
        - 3.2.0-pre.10
        - 3.2.0-pre.11
        - 3.2.0-pre.12
        - 4.0.0-pre.1
        - 4.0.0-pre.2
        compatible:
        - 3.0.6
        - 3.0.7
        - 3.0.8
        - 3.0.9
        - 3.2.0-pre.1
        - 3.2.0-pre.2
        - 3.2.0-pre.3
        - 3.2.0-pre.4
        - 3.2.0-pre.5
        - 3.2.0-pre.6
        - 3.2.0-pre.7
        - 3.2.0-pre.8
        - 3.2.0-pre.9
        - 3.2.0-pre.10
        verified: 3.0.9
      dependencies:
      - name: com.unity.ugui
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.ugui
        version: 1.0.0
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      keywords:
      - TextMeshPro
      - TextMesh Pro
      - TMP
      - Text
      - SDF
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638515173480000000
      documentationUrl: https://docs.unity3d.com/Packages/com.unity.textmeshpro@3.0/manual/index.html
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/com.unity.textmeshpro.git
        revision: 0af193626b4f76795f7cb6c9b0f55389d0e4b1d6
        path: 
    - packageId: com.unity.timeline@1.4.8
      testable: 0
      isDirectDependency: 1
      version: 1.4.8
      source: 1
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.timeline@1.4.8"
      assetPath: Packages/com.unity.timeline
      name: com.unity.timeline
      displayName: Timeline
      author:
        name: 
        email: 
        url: 
      category: 
      type: 
      description: Use Unity Timeline to create cinematic content, game-play sequences,
        audio sequences, and complex particle effects.
      status: 0
      errors: []
      versions:
        all:
        - 1.2.0
        - 1.2.1
        - 1.2.2
        - 1.2.3
        - 1.2.4
        - 1.2.5
        - 1.2.6
        - 1.2.7
        - 1.2.9
        - 1.2.10
        - 1.2.11
        - 1.2.12
        - 1.2.13
        - 1.2.14
        - 1.2.15
        - 1.2.16
        - 1.2.17
        - 1.2.18
        - 1.3.0-preview.2
        - 1.3.0-preview.3
        - 1.3.0-preview.5
        - 1.3.0-preview.6
        - 1.3.0-preview.7
        - 1.3.0
        - 1.3.1
        - 1.3.2
        - 1.3.3
        - 1.3.4
        - 1.3.5
        - 1.3.6
        - 1.3.7
        - 1.4.0-preview.1
        - 1.4.0-preview.2
        - 1.4.0-preview.3
        - 1.4.0-preview.5
        - 1.4.0-preview.6
        - 1.4.0-preview.7
        - 1.4.0
        - 1.4.1
        - 1.4.2
        - 1.4.3
        - 1.4.4
        - 1.4.5
        - 1.4.6
        - 1.4.7
        - 1.4.8
        - 1.5.0-pre.2
        - 1.5.0-preview.1
        - 1.5.0-preview.2
        - 1.5.0-preview.3
        - 1.5.0-preview.4
        - 1.5.0-preview.5
        - 1.5.1-pre.1
        - 1.5.1-pre.2
        - 1.5.1-pre.3
        - 1.5.2
        - 1.5.4
        - 1.5.5
        - 1.5.6
        - 1.5.7
        - 1.6.0-pre.1
        - 1.6.0-pre.3
        - 1.6.0-pre.4
        - 1.6.0-pre.5
        - 1.6.1
        - 1.6.2
        - 1.6.3
        - 1.6.4
        - 1.6.5
        - 1.7.0-pre.1
        - 1.7.0-pre.2
        - 1.7.0
        - 1.7.1
        - 1.7.2
        - 1.7.3
        - 1.7.4
        - 1.7.5
        - 1.7.6
        - 1.7.7
        - 1.8.0
        - 1.8.1
        - 1.8.2
        - 1.8.3
        - 1.8.4
        - 1.8.5
        - 1.8.6
        - 1.8.7
        - 1.8.8
        compatible:
        - 1.4.8
        - 1.5.0-pre.2
        - 1.5.0-preview.1
        - 1.5.0-preview.2
        - 1.5.0-preview.3
        - 1.5.0-preview.4
        - 1.5.0-preview.5
        - 1.5.1-pre.1
        - 1.5.1-pre.2
        - 1.5.1-pre.3
        - 1.5.2
        - 1.5.4
        - 1.5.5
        - 1.5.6
        - 1.5.7
        - 1.6.0-pre.1
        - 1.6.0-pre.3
        - 1.6.0-pre.4
        - 1.6.0-pre.5
        - 1.6.1
        - 1.6.2
        - 1.6.3
        - 1.6.4
        - 1.6.5
        - 1.7.0-pre.1
        - 1.7.0-pre.2
        - 1.7.0
        - 1.7.1
        - 1.7.2
        - 1.7.3
        - 1.7.4
        - 1.7.5
        - 1.7.6
        - 1.7.7
        - 1.8.0
        - 1.8.1
        - 1.8.2
        - 1.8.3
        - 1.8.4
        - 1.8.5
        - 1.8.6
        - 1.8.7
        verified: 1.4.8
      dependencies:
      - name: com.unity.modules.director
        version: 1.0.0
      - name: com.unity.modules.animation
        version: 1.0.0
      - name: com.unity.modules.audio
        version: 1.0.0
      - name: com.unity.modules.particlesystem
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.director
        version: 1.0.0
      - name: com.unity.modules.audio
        version: 1.0.0
      - name: com.unity.modules.animation
        version: 1.0.0
      - name: com.unity.modules.particlesystem
        version: 1.0.0
      keywords:
      - unity
      - animation
      - editor
      - timeline
      - tools
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 637557616100000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/com.unity.timeline.git
        revision: 527e4364e28028f20f04fc51db671eb4bb910a26
        path: 
    - packageId: com.unity.toolchain.win-x86_64-linux-x86_64@2.0.10
      testable: 0
      isDirectDependency: 1
      version: 2.0.10
      source: 1
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.toolchain.win-x86_64-linux-x86_64@2.0.10"
      assetPath: Packages/com.unity.toolchain.win-x86_64-linux-x86_64
      name: com.unity.toolchain.win-x86_64-linux-x86_64
      displayName: Toolchain Win Linux x64
      author:
        name: 
        email: 
        url: 
      category: 
      type: 
      description: Cross-compilation toolchain to build player target Linux x86_64
        on host Windows x86_64
      status: 0
      errors: []
      versions:
        all:
        - 0.1.4-preview
        - 0.1.5-preview
        - 0.1.6-preview
        - 0.1.7-preview
        - 0.1.8-preview
        - 0.1.9-preview
        - 0.1.10-preview
        - 0.1.11-preview
        - 0.1.13-preview
        - 0.1.14-preview
        - 0.1.15-preview
        - 0.1.16-preview
        - 0.1.17-preview
        - 0.1.18-preview
        - 0.1.19-preview
        - 0.1.20-preview
        - 0.1.21-preview
        - 1.0.0
        - 2.0.0
        - 2.0.1
        - 2.0.2
        - 2.0.3
        - 2.0.4
        - 2.0.5
        - 2.0.6
        - 2.0.9
        - 2.0.10
        compatible:
        - 0.1.4-preview
        - 0.1.5-preview
        - 0.1.6-preview
        - 0.1.7-preview
        - 0.1.8-preview
        - 0.1.9-preview
        - 0.1.10-preview
        - 0.1.11-preview
        - 0.1.13-preview
        - 0.1.14-preview
        - 0.1.15-preview
        - 0.1.16-preview
        - 0.1.17-preview
        - 0.1.18-preview
        - 0.1.19-preview
        - 0.1.20-preview
        - 0.1.21-preview
        - 1.0.0
        - 2.0.0
        - 2.0.1
        - 2.0.2
        - 2.0.3
        - 2.0.4
        - 2.0.5
        - 2.0.6
        - 2.0.9
        - 2.0.10
        verified: 
      dependencies:
      - name: com.unity.sysroot
        version: 2.0.10
      - name: com.unity.sysroot.linux-x86_64
        version: 2.0.9
      resolvedDependencies:
      - name: com.unity.sysroot
        version: 2.0.10
      - name: com.unity.sysroot.linux-x86_64
        version: 2.0.9
      keywords:
      - toolchain
      - windows
      - linux
      - cross-compilation
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638689842850000000
      documentationUrl: https://docs.unity3d.com/Packages/com.unity.toolchain.win-x86_64-linux-x86_64@2.0/manual/index.html
      hasRepository: 1
      repository:
        type: git
        url: https://github.com/Unity-Technologies/com.unity.sysroot.git
        revision: c5a32c15f32b7de0b0aa81c256ea52b322478b15
        path: 
    - packageId: com.unity.ugui@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.ugui@1.0.0"
      assetPath: Packages/com.unity.ugui
      name: com.unity.ugui
      displayName: Unity UI
      author:
        name: 
        email: 
        url: 
      category: 
      type: 
      description: "Unity UI is a UI toolkit for developing user interfaces for games
        and applications. It is a GameObject-based UI system that uses Components
        and the Game View to arrange, position, and style user interfaces. \u200B
        You cannot use Unity UI to create or change user interfaces in the Unity
        Editor."
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        - 3.0.0-exp.1
        - 3.0.0-exp.3
        - 3.0.0-exp.4
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      keywords:
      - UI
      - ugui
      - Unity UI
      - Canvas
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.ai@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.ai@1.0.0"
      assetPath: Packages/com.unity.modules.ai
      name: com.unity.modules.ai
      displayName: AI
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The AI module implements the path finding features in Unity.
        Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AIModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.androidjni@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.androidjni@1.0.0"
      assetPath: Packages/com.unity.modules.androidjni
      name: com.unity.modules.androidjni
      displayName: Android JNI
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'AndroidJNI module allows you to call Java code. Scripting API:
        https://docs.unity3d.com/ScriptReference/UnityEngine.AndroidJNIModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.animation@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.animation@1.0.0"
      assetPath: Packages/com.unity.modules.animation
      name: com.unity.modules.animation
      displayName: Animation
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Animation module implements Unity''s animation system. Scripting
        API: https://docs.unity3d.com/ScriptReference/UnityEngine.AnimationModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.assetbundle@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.assetbundle@1.0.0"
      assetPath: Packages/com.unity.modules.assetbundle
      name: com.unity.modules.assetbundle
      displayName: Asset Bundle
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The AssetBundle module implements the AssetBundle class and related
        APIs to load data from AssetBundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AssetBundleModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.audio@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.audio@1.0.0"
      assetPath: Packages/com.unity.modules.audio
      name: com.unity.modules.audio
      displayName: Audio
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Audio module implements Unity''s audio system. Scripting
        API: https://docs.unity3d.com/ScriptReference/UnityEngine.AudioModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.autostreaming@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.autostreaming@1.0.0"
      assetPath: Packages/com.unity.modules.autostreaming
      name: com.unity.modules.autostreaming
      displayName: Auto Streaming
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The AutoStreaming module implements the resource auto-streaming
        feature in Unity. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AutoStreamingModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.animation
        version: 1.0.0
      - name: com.unity.modules.assetbundle
        version: 1.0.0
      - name: com.unity.modules.audio
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.modules.unitywebrequestassetbundle
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.animation
        version: 1.0.0
      - name: com.unity.modules.assetbundle
        version: 1.0.0
      - name: com.unity.modules.audio
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.modules.unitywebrequestassetbundle
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.cloth@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.cloth@1.0.0"
      assetPath: Packages/com.unity.modules.cloth
      name: com.unity.modules.cloth
      displayName: Cloth
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Cloth module implements cloth physics simulation through
        the Cloth component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ClothModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.physics
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.physics
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.director@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.director@1.0.0"
      assetPath: Packages/com.unity.modules.director
      name: com.unity.modules.director
      displayName: Director
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Director module implements the PlayableDirector class. Scripting
        API: https://docs.unity3d.com/ScriptReference/UnityEngine.DirectorModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.audio
        version: 1.0.0
      - name: com.unity.modules.animation
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.audio
        version: 1.0.0
      - name: com.unity.modules.animation
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.imageconversion@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.imageconversion@1.0.0"
      assetPath: Packages/com.unity.modules.imageconversion
      name: com.unity.modules.imageconversion
      displayName: Image Conversion
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The ImageConversion module implements the ImageConversion class
        which provides helper methods to convert images from and to PNG, JPEG or
        EXR formats. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ImageConversionModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.imgui@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.imgui@1.0.0"
      assetPath: Packages/com.unity.modules.imgui
      name: com.unity.modules.imgui
      displayName: IMGUI
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The IMGUI module provides Unity''s immediate mode GUI solution
        for creating in-game and editor user interfaces. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.IMGUIModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.jsonserialize@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.jsonserialize@1.0.0"
      assetPath: Packages/com.unity.modules.jsonserialize
      name: com.unity.modules.jsonserialize
      displayName: JSONSerialize
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The JSONSerialize module provides the JsonUtility class which
        lets you serialize Unity Objects to JSON format. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.JSONSerializeModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.particlesystem@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.particlesystem@1.0.0"
      assetPath: Packages/com.unity.modules.particlesystem
      name: com.unity.modules.particlesystem
      displayName: Particle System
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The ParticleSystem module implements Unity''s Particle System.
        Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ParticleSystemModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.physics@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.physics@1.0.0"
      assetPath: Packages/com.unity.modules.physics
      name: com.unity.modules.physics
      displayName: Physics
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Physics module implements 3D physics in Unity. Scripting
        API: https://docs.unity3d.com/ScriptReference/UnityEngine.PhysicsModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.physics2d@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.physics2d@1.0.0"
      assetPath: Packages/com.unity.modules.physics2d
      name: com.unity.modules.physics2d
      displayName: Physics 2D
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Physics2d module implements 2D physics in Unity. Scripting
        API: https://docs.unity3d.com/ScriptReference/UnityEngine.Physics2DModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.screencapture@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.screencapture@1.0.0"
      assetPath: Packages/com.unity.modules.screencapture
      name: com.unity.modules.screencapture
      displayName: Screen Capture
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The ScreenCapture module provides functionality to take screen
        shots using the ScreenCapture class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ScreenCaptureModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.imageconversion
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.imageconversion
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.terrain@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.terrain@1.0.0"
      assetPath: Packages/com.unity.modules.terrain
      name: com.unity.modules.terrain
      displayName: Terrain
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Terrain module implements Unity''s Terrain rendering engine
        available through the Terrain component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.terrainphysics@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.terrainphysics@1.0.0"
      assetPath: Packages/com.unity.modules.terrainphysics
      name: com.unity.modules.terrainphysics
      displayName: Terrain Physics
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The TerrainPhysics module connects the Terrain and Physics modules
        by implementing the TerrainCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainPhysicsModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.physics
        version: 1.0.0
      - name: com.unity.modules.terrain
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.physics
        version: 1.0.0
      - name: com.unity.modules.terrain
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.tilemap@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.tilemap@1.0.0"
      assetPath: Packages/com.unity.modules.tilemap
      name: com.unity.modules.tilemap
      displayName: Tilemap
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Tilemap module implements the Tilemap class. Scripting API:
        https://docs.unity3d.com/ScriptReference/UnityEngine.TilemapModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.physics2d
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.physics2d
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.ui@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.ui@1.0.0"
      assetPath: Packages/com.unity.modules.ui
      name: com.unity.modules.ui
      displayName: UI
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The UI module implements basic components required for Unity''s
        UI system Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.uielements@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.uielements@1.0.0"
      assetPath: Packages/com.unity.modules.uielements
      name: com.unity.modules.uielements
      displayName: UIElements
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The UIElements module implements the UIElements retained mode
        UI framework. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIElementsModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.uielementsnative
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.uielementsnative
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.umbra@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.umbra@1.0.0"
      assetPath: Packages/com.unity.modules.umbra
      name: com.unity.modules.umbra
      displayName: Umbra
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Umbra module implements Unity''s occlusion culling system.
        Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UmbraModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.unityanalytics@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.unityanalytics@1.0.0"
      assetPath: Packages/com.unity.modules.unityanalytics
      name: com.unity.modules.unityanalytics
      displayName: Unity Analytics
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The UnityAnalytics module implements APIs required to use Unity
        Analytics. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityAnalyticsModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.unitywebrequest@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.unitywebrequest@1.0.0"
      assetPath: Packages/com.unity.modules.unitywebrequest
      name: com.unity.modules.unitywebrequest
      displayName: Unity Web Request
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The UnityWebRequest module lets you communicate with http services.
        Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.unitywebrequestassetbundle@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.unitywebrequestassetbundle@1.0.0"
      assetPath: Packages/com.unity.modules.unitywebrequestassetbundle
      name: com.unity.modules.unitywebrequestassetbundle
      displayName: Unity Web Request Asset Bundle
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The UnityWebRequestAssetBundle module provides the DownloadHandlerAssetBundle
        class to use UnityWebRequest to download Asset Bundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAssetBundleModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.assetbundle
        version: 1.0.0
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.assetbundle
        version: 1.0.0
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.unitywebrequestaudio@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.unitywebrequestaudio@1.0.0"
      assetPath: Packages/com.unity.modules.unitywebrequestaudio
      name: com.unity.modules.unitywebrequestaudio
      displayName: Unity Web Request Audio
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The UnityWebRequestAudio module provides the DownloadHandlerAudioClip
        class to use UnityWebRequest to download AudioClips. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAudioModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.modules.audio
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.modules.audio
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.unitywebrequesttexture@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.unitywebrequesttexture@1.0.0"
      assetPath: Packages/com.unity.modules.unitywebrequesttexture
      name: com.unity.modules.unitywebrequesttexture
      displayName: Unity Web Request Texture
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The UnityWebRequestTexture module provides the DownloadHandlerTexture
        class to use UnityWebRequest to download Textures. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestTextureModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.modules.imageconversion
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.modules.imageconversion
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.unitywebrequestwww@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.unitywebrequestwww@1.0.0"
      assetPath: Packages/com.unity.modules.unitywebrequestwww
      name: com.unity.modules.unitywebrequestwww
      displayName: Unity Web Request WWW
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The UnityWebRequestWWW module implements the legacy WWW lets
        you communicate with http services. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestWWWModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.modules.unitywebrequestassetbundle
        version: 1.0.0
      - name: com.unity.modules.unitywebrequestaudio
        version: 1.0.0
      - name: com.unity.modules.audio
        version: 1.0.0
      - name: com.unity.modules.assetbundle
        version: 1.0.0
      - name: com.unity.modules.imageconversion
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.modules.unitywebrequestassetbundle
        version: 1.0.0
      - name: com.unity.modules.assetbundle
        version: 1.0.0
      - name: com.unity.modules.unitywebrequestaudio
        version: 1.0.0
      - name: com.unity.modules.audio
        version: 1.0.0
      - name: com.unity.modules.imageconversion
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.vehicles@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.vehicles@1.0.0"
      assetPath: Packages/com.unity.modules.vehicles
      name: com.unity.modules.vehicles
      displayName: Vehicles
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Vehicles module implements vehicle physics simulation through
        the WheelCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VehiclesModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.physics
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.physics
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.video@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.video@1.0.0"
      assetPath: Packages/com.unity.modules.video
      name: com.unity.modules.video
      displayName: Video
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Video module lets you play back video files in your content.
        Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VideoModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.audio
        version: 1.0.0
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.audio
        version: 1.0.0
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.vr@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.vr@1.0.0"
      assetPath: Packages/com.unity.modules.vr
      name: com.unity.modules.vr
      displayName: VR
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The VR module implements support for virtual reality devices
        in Unity. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VRModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.physics
        version: 1.0.0
      - name: com.unity.modules.xr
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.physics
        version: 1.0.0
      - name: com.unity.modules.xr
        version: 1.0.0
      - name: com.unity.modules.subsystems
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.wind@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.wind@1.0.0"
      assetPath: Packages/com.unity.modules.wind
      name: com.unity.modules.wind
      displayName: Wind
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Wind module implements the WindZone component which can affect
        terrain rendering and particle simulations. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.WindModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.xr@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.xr@1.0.0"
      assetPath: Packages/com.unity.modules.xr
      name: com.unity.modules.xr
      displayName: XR
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The XR module contains the VR and AR related platform support
        functionality. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.XRModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.physics
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.subsystems
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.physics
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.subsystems
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.subsystems@1.0.0
      testable: 0
      isDirectDependency: 0
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.subsystems@1.0.0"
      assetPath: Packages/com.unity.modules.subsystems
      name: com.unity.modules.subsystems
      displayName: Subsystems
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Subsystem module contains the definitions and runtime support
        for general subsystems in Unity. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.SubsystemsModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.uielementsnative@1.0.0
      testable: 0
      isDirectDependency: 0
      version: 1.0.0
      source: 2
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.modules.uielementsnative@1.0.0"
      assetPath: Packages/com.unity.modules.uielementsnative
      name: com.unity.modules.uielementsnative
      displayName: UIElements Native
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.sysroot@2.0.10
      testable: 0
      isDirectDependency: 0
      version: 2.0.10
      source: 1
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.sysroot@2.0.10"
      assetPath: Packages/com.unity.sysroot
      name: com.unity.sysroot
      displayName: Sysroot Base
      author:
        name: 
        email: 
        url: 
      category: 
      type: 
      description: This is the base sysroot package required by all sysroot and toolchain
        packages
      status: 0
      errors: []
      versions:
        all:
        - 0.1.7-preview
        - 0.1.8-preview
        - 0.1.9-preview
        - 0.1.10-preview
        - 0.1.11-preview
        - 0.1.12-preview
        - 0.1.14-preview
        - 0.1.15-preview
        - 0.1.16-preview
        - 0.1.17-preview
        - 0.1.18-preview
        - 0.1.19-preview
        - 1.0.0
        - 2.0.0
        - 2.0.2
        - 2.0.3
        - 2.0.4
        - 2.0.5
        - 2.0.6
        - 2.0.7
        - 2.0.10
        compatible:
        - 0.1.7-preview
        - 0.1.8-preview
        - 0.1.9-preview
        - 0.1.10-preview
        - 0.1.11-preview
        - 0.1.12-preview
        - 0.1.14-preview
        - 0.1.15-preview
        - 0.1.16-preview
        - 0.1.17-preview
        - 0.1.18-preview
        - 0.1.19-preview
        - 1.0.0
        - 2.0.0
        - 2.0.2
        - 2.0.3
        - 2.0.4
        - 2.0.5
        - 2.0.6
        - 2.0.7
        - 2.0.10
        verified: 
      dependencies: []
      resolvedDependencies: []
      keywords:
      - unity
      - linux
      - sysroot
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638500638510000000
      documentationUrl: https://docs.unity3d.com/Packages/com.unity.sysroot@2.0/manual/index.html
      hasRepository: 1
      repository:
        type: git
        url: https://github.com/Unity-Technologies/com.unity.sysroot.git
        revision: 48e503c8f30007e813fbcec90dc84795285406b4
        path: 
    - packageId: com.unity.sysroot.linux-x86_64@2.0.9
      testable: 0
      isDirectDependency: 0
      version: 2.0.9
      source: 1
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.sysroot.linux-x86_64@2.0.9"
      assetPath: Packages/com.unity.sysroot.linux-x86_64
      name: com.unity.sysroot.linux-x86_64
      displayName: Sysroot Linux x64
      author:
        name: 
        email: 
        url: 
      category: 
      type: 
      description: Sysroot used to build unity player for linux x86_64
      status: 0
      errors: []
      versions:
        all:
        - 0.1.6-preview
        - 0.1.7-preview
        - 0.1.8-preview
        - 0.1.9-preview
        - 0.1.10-preview
        - 0.1.11-preview
        - 0.1.12-preview
        - 0.1.13-preview
        - 0.1.14-preview
        - 0.1.15-preview
        - 1.0.0
        - 2.0.0
        - 2.0.1
        - 2.0.2
        - 2.0.3
        - 2.0.4
        - 2.0.5
        - 2.0.6
        - 2.0.9
        compatible:
        - 0.1.6-preview
        - 0.1.7-preview
        - 0.1.8-preview
        - 0.1.9-preview
        - 0.1.10-preview
        - 0.1.11-preview
        - 0.1.12-preview
        - 0.1.13-preview
        - 0.1.14-preview
        - 0.1.15-preview
        - 1.0.0
        - 2.0.0
        - 2.0.1
        - 2.0.2
        - 2.0.3
        - 2.0.4
        - 2.0.5
        - 2.0.6
        - 2.0.9
        verified: 
      dependencies:
      - name: com.unity.sysroot
        version: 2.0.10
      resolvedDependencies:
      - name: com.unity.sysroot
        version: 2.0.10
      keywords:
      - unity
      - linux
      - sysroot
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638500638600000000
      documentationUrl: https://docs.unity3d.com/Packages/com.unity.sysroot.linux-x86_64@2.0/manual/index.html
      hasRepository: 1
      repository:
        type: git
        url: https://github.com/Unity-Technologies/com.unity.sysroot.git
        revision: bf7a008256a1175e0f8a0d7d26d6826cbfbe1bb2
        path: 
    - packageId: com.unity.ext.nunit@1.0.6
      testable: 0
      isDirectDependency: 0
      version: 1.0.6
      source: 1
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.ext.nunit@1.0.6"
      assetPath: Packages/com.unity.ext.nunit
      name: com.unity.ext.nunit
      displayName: Custom NUnit
      author:
        name: 
        email: 
        url: 
      category: Libraries
      type: 
      description: Custom version of the nunit package build to work with Unity.
        Used by the Unity Test Framework.
      status: 0
      errors: []
      versions:
        all:
        - 0.1.5-preview
        - 0.1.6-preview
        - 0.1.9-preview
        - 1.0.0
        - 1.0.5
        - 1.0.6
        - 2.0.2
        - 2.0.3
        - 2.0.4
        - 2.0.5
        compatible:
        - 0.1.5-preview
        - 0.1.6-preview
        - 0.1.9-preview
        - 1.0.0
        - 1.0.5
        - 1.0.6
        - 2.0.2
        - 2.0.3
        - 2.0.4
        - 2.0.5
        verified: 1.0.6
      dependencies: []
      resolvedDependencies: []
      keywords:
      - nunit
      - unittest
      - test
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 637429759280000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/com.unity.ext.nunit.git
        revision: 29ea4d6504a5f58fb3a6934db839aa80ae6d9d88
        path: 
    - packageId: com.unity.nuget.newtonsoft-json@3.0.2
      testable: 0
      isDirectDependency: 0
      version: 3.0.2
      source: 1
      resolvedPath: "D:\\Unity\\\u53F8\u5BFC\\SIP2.0\\Library\\PackageCache\\com.unity.nuget.newtonsoft-json@3.0.2"
      assetPath: Packages/com.unity.nuget.newtonsoft-json
      name: com.unity.nuget.newtonsoft-json
      displayName: Newtonsoft Json
      author:
        name: 
        email: 
        url: 
      category: 
      type: library
      description: 'Newtonsoft Json for use in Unity projects and Unity packages.
        Currently synced to version 13.0.1.


        This package is used for advanced
        json serialization and deserialization. Most Unity users will be better suited
        using the existing json tools built into Unity.

        To avoid assembly
        clashes, please use this package if you intend to use Newtonsoft Json.'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0-preview.2
        - 1.0.0-preview.3
        - 1.0.0-preview.4
        - 1.0.1-preview.1
        - 1.1.2
        - 2.0.0-preview
        - 2.0.0-preview.1
        - 2.0.0-preview.2
        - 2.0.0
        - 2.0.1-preview.1
        - 2.0.2
        - 3.0.1
        - 3.0.2
        - 3.1.0
        - 3.2.0
        - 3.2.1
        compatible:
        - 1.0.0-preview.2
        - 1.0.0-preview.3
        - 1.0.0-preview.4
        - 1.0.1-preview.1
        - 1.1.2
        - 2.0.0-preview
        - 2.0.0-preview.1
        - 2.0.0-preview.2
        - 2.0.0
        - 2.0.1-preview.1
        - 2.0.2
        - 3.0.1
        - 3.0.2
        - 3.1.0
        - 3.2.0
        - 3.2.1
        verified: 3.2.1
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.cn
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 637846828220000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/com.unity.nuget.newtonsoft-json.git
        revision: 8f66870a71ad3f2cdd46330b5c482dfd138ffc64
        path: 
  m_LocalPackages:
    m_LocalFileStatus:
    - m_FilePath: "C:\\Users\\<USER>\\OneDrive\\\u6587\u6863\\Cline\\MCP\\unity-mcp\\UnityMcpBridge/package.json"
      m_PathExists: 1
      m_ContentTrackingEnabled: 1
      m_ModificationDate:
        serializedVersion: 2
        ticks: 638863256981014575
      m_Hash: 2971685502
    - m_FilePath: "C:\\Users\\<USER>\\OneDrive\\\u6587\u6863\\Cline\\MCP\\unity-mcp\\UnityMcpBridge"
      m_PathExists: 1
      m_ContentTrackingEnabled: 1
      m_ModificationDate:
        serializedVersion: 2
        ticks: 0
      m_Hash: 0
m_ProjectPath: "D:/Unity/\u53F8\u5BFC/SIP2.0/Packages"
m_EditorVersion: 2020.3.48f1c1 (06fbdfbf16e3)

using UnityEngine;
using SIPSorcery.SIP;

/// <summary>
/// SIP连接状态检查工具
/// 用于检查SIP会话是否正确建立，这是音频发送的前提条件
/// </summary>
public class SIPConnectionChecker : MonoBehaviour
{
    [Header("SIP连接检查")]
    public KeyCode checkSIPConnectionKey = KeyCode.F7;
    
    void Start()
    {
        Debug.Log("[SIPConnectionChecker] SIP连接状态检查工具已启动");
        Debug.Log($"  - 按 {checkSIPConnectionKey} 检查SIP连接状态");
    }
    
    void Update()
    {
        if (Input.GetKeyDown(checkSIPConnectionKey))
        {
            CheckSIPConnection();
        }
    }
    
    /// <summary>
    /// 检查SIP连接状态
    /// </summary>
    private void CheckSIPConnection()
    {
        Debug.Log("=== SIP连接状态检查 ===");
        
        // 1. 检查SIPClient
        CheckSIPClient();
        
        // 2. 检查MediaManager
        CheckMediaManager();
        
        // 3. 检查AudioManager的媒体会话
        CheckAudioMediaSession();
        
        // 4. 检查RTP通道状态
        CheckRTPChannelStatus();
        
        Debug.Log("=== SIP连接检查完成 ===");
    }
    
    /// <summary>
    /// 检查SIPClient状态
    /// </summary>
    private void CheckSIPClient()
    {
        Debug.Log("--- 1. SIPClient状态 ---");
        
        var sipClient = FindObjectOfType<SIPClient>();
        if (sipClient == null)
        {
            Debug.LogError("❌ 未找到SIPClient组件！");
            return;
        }
        
        Debug.Log("✅ 找到SIPClient组件");
        
        // 检查SIP连接状态
        // 注意：这里需要根据SIPClient的实际API来检查状态
        Debug.Log("SIPClient组件存在，但需要检查具体的连接状态");
        Debug.Log("💡 请确保已成功注册到SIP服务器并建立了通话");
    }
    
    /// <summary>
    /// 检查MediaManager状态
    /// </summary>
    private void CheckMediaManager()
    {
        Debug.Log("--- 2. MediaManager状态 ---");
        
        var mediaManager = FindObjectOfType<SIPSorcery.MediaManager>();
        if (mediaManager == null)
        {
            Debug.LogError("❌ 未找到MediaManager组件！");
            return;
        }
        
        Debug.Log("✅ 找到MediaManager组件");
        
        // 检查媒体管理器状态
        Debug.Log("MediaManager组件存在");
    }
    
    /// <summary>
    /// 检查AudioManager的媒体会话
    /// </summary>
    private void CheckAudioMediaSession()
    {
        Debug.Log("--- 3. AudioManager媒体会话状态 ---");
        
        var audioManager = FindObjectOfType<AudioManager>();
        if (audioManager == null)
        {
            Debug.LogError("❌ 未找到AudioManager组件！");
            return;
        }
        
        Debug.Log("✅ 找到AudioManager组件");
        
        // 检查音频会话状态
        bool isSessionStarted = audioManager.IsAudioSessionStarted();
        Debug.Log($"音频会话启动状态: {(isSessionStarted ? "✅ 已启动" : "❌ 未启动")}");
        
        var mediaSession = audioManager.GetAudioSession();
        if (mediaSession == null)
        {
            Debug.LogError("❌ 音频媒体会话为空！");
            Debug.LogError("   这意味着SIP通话可能没有建立或媒体协商失败");
            return;
        }
        
        Debug.Log("✅ 音频媒体会话存在");
        
        // 检查音频流状态
        if (mediaSession.AudioStream != null)
        {
            Debug.Log("✅ 音频流存在");
            
            var destEndPoint = mediaSession.AudioStream.DestinationEndPoint;
            if (destEndPoint != null)
            {
                Debug.Log($"✅ RTP目标端点: {destEndPoint}");
            }
            else
            {
                Debug.LogError("❌ RTP目标端点为空！");
                Debug.LogError("   这是导致'An empty destination was specified to Send'错误的原因");
            }
            
            var localEndPoint = mediaSession.AudioStream.LocalEndPoint;
            if (localEndPoint != null)
            {
                Debug.Log($"✅ RTP本地端点: {localEndPoint}");
            }
            else
            {
                Debug.LogWarning("⚠️ RTP本地端点为空");
            }
        }
        else
        {
            Debug.LogError("❌ 音频流为空！");
        }
    }
    
    /// <summary>
    /// 检查RTP通道状态
    /// </summary>
    private void CheckRTPChannelStatus()
    {
        Debug.Log("--- 4. RTP通道状态 ---");
        
        var audioManager = FindObjectOfType<AudioManager>();
        if (audioManager == null) return;
        
        var mediaSession = audioManager.GetAudioSession();
        if (mediaSession == null) return;
        
        if (mediaSession.AudioStream != null)
        {
            // 检查RTP会话状态
            Debug.Log($"音频流状态: {mediaSession.AudioStream.StreamStatus}");
            Debug.Log($"音频流方向: {mediaSession.AudioStream.MediaDirection}");
            
            // 检查是否可以发送
            bool canSend = mediaSession.AudioStream.DestinationEndPoint != null;
            Debug.Log($"可以发送RTP包: {(canSend ? "✅ 是" : "❌ 否")}");
            
            if (!canSend)
            {
                Debug.LogError("🔥 关键问题：无法发送RTP包！");
                Debug.LogError("   可能原因：");
                Debug.LogError("   1. SIP通话没有建立");
                Debug.LogError("   2. SDP媒体协商失败");
                Debug.LogError("   3. 对方没有提供音频端点信息");
                Debug.LogError("   4. 网络连接问题");
            }
        }
    }
    
    /// <summary>
    /// 在GUI中显示SIP连接状态
    /// </summary>
    void OnGUI()
    {
        GUILayout.BeginArea(new Rect(Screen.width - 320, 220, 300, 150));
        GUILayout.BeginVertical("box");
        
        GUILayout.Label("SIP连接状态");
        
        // SIPClient状态
        var sipClient = FindObjectOfType<SIPClient>();
        GUILayout.Label($"SIPClient: {(sipClient != null ? "✅" : "❌")}");
        
        // AudioManager状态
        var audioManager = FindObjectOfType<AudioManager>();
        bool audioSessionOK = audioManager != null && audioManager.IsAudioSessionStarted();
        GUILayout.Label($"音频会话: {(audioSessionOK ? "✅" : "❌")}");
        
        // RTP端点状态
        bool rtpEndpointOK = false;
        if (audioManager != null)
        {
            var mediaSession = audioManager.GetAudioSession();
            rtpEndpointOK = mediaSession?.AudioStream?.DestinationEndPoint != null;
        }
        GUILayout.Label($"RTP端点: {(rtpEndpointOK ? "✅" : "❌")}");
        
        // 检查按钮
        if (GUILayout.Button($"检查SIP连接 ({checkSIPConnectionKey})"))
        {
            CheckSIPConnection();
        }
        
        // 状态提示
        if (!rtpEndpointOK)
        {
            GUILayout.Label("⚠️ 请先建立SIP通话", GUI.skin.box);
        }
        
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
}

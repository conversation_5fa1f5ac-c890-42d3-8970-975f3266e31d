# G722解码PCM音频保存功能使用说明

## 📅 更新日期：2025-07-16

## 🎯 功能概述
在Unity SIP项目中，已经实现了自动保存接听后从G722解码得到的PCM（Pulse-Code Modulation）音频数据功能，用于测试和调试G722解码脚本问题。

**音频处理流程：**
```
RTP包接收 → G722压缩数据提取 → G722解码 → PCM原始音频数据 → 保存到WAV文件
```

## 🔧 功能特性

### 1. 自动PCM保存
- **自动触发**：每当接收到G722音频RTP包并成功解码为PCM数据后，自动保存这些PCM音频数据
- **数据来源**：G722解码器输出的原始PCM音频数据（16kHz, 16位, 单声道）
- **文件格式**：标准WAV格式，包含完整的WAV文件头
- **保存位置**：`Application.persistentDataPath/AudioDebug/`
- **文件命名**：`decoded_audio_yyyyMMdd_HHmmss_fff.wav`

### 2. PCM音频参数
- **采样率**：16000Hz（G722解码输出的标准采样率）
- **位深度**：16位有符号整数
- **声道数**：1（单声道）
- **数据格式**：小端序PCM原始音频数据
- **数据特点**：未压缩的数字音频，直接可用于播放或分析

### 3. 保存限制
- **最大时长**：30秒音频（480,000样本）
- **自动停止**：达到最大样本数后自动停止保存
- **错误处理**：保存失败时自动禁用保存功能并记录错误

## 📁 文件保存详情

### 保存路径
```
Windows: C:\Users\<USER>\AppData\LocalLow\[公司名]\[项目名]\AudioDebug\
Android: /storage/emulated/0/Android/data/[包名]/files/AudioDebug/
```

### 文件命名规则
```
decoded_audio_20250716_143025_123.wav  // G722解码得到的PCM音频数据
playback_audio_20250716_143025_123.wav // Unity实际播放的音频数据（用于对比）
```

### 文件冲突处理
如果文件名已存在，会自动添加序号：
```
decoded_audio_20250716_143025_123_001.wav
decoded_audio_20250716_143025_123_002.wav
```

## 🚀 使用方法

### 1. 自动保存（默认启用）
```csharp
// 功能默认启用，无需额外配置
// 当AudioExtrasSink接收到G722数据时自动保存PCM
```

### 2. 手动控制保存
```csharp
// 获取AudioExtrasSink组件
var audioSink = FindObjectOfType<AudioExtrasSink>();

// 启用/禁用音频保存
audioSink.SetAudioSavingEnabled(true);  // 启用
audioSink.SetAudioSavingEnabled(false); // 禁用
```

### 3. 使用测试工具
将`G722PCMSaveTest.cs`添加到场景中的任意GameObject：

```csharp
// 快捷键功能
F9  - 运行完整的G722解码测试（5秒测试音频）
F10 - 检查音频保存路径和现有文件
F11 - 生成单帧测试G722数据并解码
```

## 📊 日志监控

### 初始化日志
```
[AudioExtrasSink] 音频保存功能已初始化:
[AudioExtrasSink] - G722解码PCM音频: C:\Users\<USER>\decoded_audio_20250716_143025_123.wav
[AudioExtrasSink] - Unity播放音频: C:\Users\<USER>\playback_audio_20250716_143025_123.wav
```

### 保存进度日志
```
[AudioExtrasSink] 已保存G722解码音频: 1.0秒 (16000样本PCM数据)
[AudioExtrasSink] 已保存G722解码音频: 2.0秒 (32000样本PCM数据)
[AudioExtrasSink] 已保存G722解码音频: 3.0秒 (48000样本PCM数据)
```

### 错误日志
```
[AudioExtrasSink] 保存解码音频失败: 磁盘空间不足
[AudioExtrasSink] 初始化音频保存失败: 无法创建目录
```

## 🔍 调试和验证

### 1. 检查保存状态
```csharp
// 在Unity Console中查看日志
// 确认看到"音频保存功能已初始化"消息
// 确认看到定期的"已保存解码音频"进度消息
```

### 2. 验证文件内容
```csharp
// 使用音频编辑软件（如Audacity）打开WAV文件
// 参数应为：16000Hz, 16位, 单声道
// 可以听到解码后的音频内容
```

### 3. 使用测试工具
```csharp
// 添加G722PCMSaveTest组件到场景
// 按F9运行完整测试
// 按F10检查保存路径和文件
// 按F11测试单帧解码
```

## ⚠️ 注意事项

### 1. 存储空间
- 16kHz 16位单声道音频约占用32KB/秒
- 30秒音频文件约960KB
- 确保设备有足够存储空间

### 2. 性能影响
- 文件写入操作在音频线程中执行
- 对于长时间通话，建议定期清理旧文件
- 可通过`SetAudioSavingEnabled(false)`禁用保存以提升性能

### 3. 文件访问
- 音频文件在通话结束后才完全写入
- 通话过程中文件可能不完整
- 建议在通话结束后访问保存的文件

## 🛠️ 故障排除

### 问题1：没有生成音频文件
**可能原因**：
- AudioExtrasSink未初始化
- 音频保存功能被禁用
- 磁盘空间不足
- 权限问题

**解决方案**：
1. 检查Unity Console中的初始化日志
2. 确认`_enableAudioSaving = true`
3. 检查磁盘空间和写入权限
4. 使用F10快捷键检查保存路径

### 问题2：音频文件损坏或无法播放
**可能原因**：
- WAV文件头信息不正确
- 文件写入过程中被中断
- 音频数据格式错误

**解决方案**：
1. 等待通话完全结束后再访问文件
2. 检查文件大小是否合理
3. 使用专业音频软件验证文件格式

### 问题3：保存功能自动禁用
**可能原因**：
- 文件写入异常
- 达到最大保存样本数限制
- 系统资源不足

**解决方案**：
1. 检查Unity Console中的错误日志
2. 清理旧的音频文件释放空间
3. 重启应用重新初始化保存功能

## 📋 技术实现细节

### 代码位置
- **主要实现**：`Assets/Scripts/Media/AudioExtrasSink.cs`
- **测试工具**：`Assets/Scripts/test/G722PCMSaveTest.cs`
- **调用位置**：`GotAudioRtp()` -> G722解码 -> `SaveDecodedAudio()`

### 关键方法
```csharp
// G722解码后立即保存PCM数据
SaveDecodedAudio(actualDecoded);

// WAV文件头写入
WriteWavHeader(_decodedWriter, G722_PCM_SAMPLE_RATE, 1, 16);

// PCM数据写入
_decodedWriter.Write(pcmData[i]);
```

### 数据流程
```
RTP包接收 -> G722解码 -> PCM数据 -> SaveDecodedAudio() -> WAV文件
```

这个功能为调试G722解码问题提供了完整的PCM音频数据，可以用于分析音频质量、验证解码正确性和排查音频播放问题。

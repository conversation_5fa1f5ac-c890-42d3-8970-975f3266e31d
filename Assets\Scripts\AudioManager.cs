/*
 * 音频管理器
 * 负责 音频的采集、编码、传输和播放
 * 从 MediaManager 中拆分出的独立音频管理模块
 */

using UnityEngine;
using SIPSorceryMedia.Abstractions;
using System;
using System.Threading.Tasks;
using System.Net;
using SIPSorcery.Net;
using SIPSorcery.Media;
using UnityEngine.Audio;
using System.Diagnostics;

/// <summary>
/// 音频管理器
/// 负责音频的采集、编码、传输和播放
/// </summary>
public class AudioManager : MonoBehaviour
{
    [Header("音频配置")]

    private SIPClient _sipClient;
    private VoIPMediaSession _mediaSession;
    private IAudioSource _audioSource;
    private IAudioSink _audioSink;
    private bool _isRtpChannelReady;

    // RTP 流配置
    private const int PCMU_PAYLOAD_TYPE = 0;    // G.711 PCMU 音频的 payload type
    private const int PCMA_PAYLOAD_TYPE = 8;    // G.711 PCMA 音频的 payload type
    private const int G722_PAYLOAD_TYPE = 9;    // G.722 音频的 payload type
    private uint _audioSsrc;                    // 音频流的 SSRC
    private Stopwatch _rtpArrivalStopwatch = new Stopwatch(); // 用于RTP包到达时序分析

    // 音频格式配置
    private AudioFormat _audioFormat;           // 当前使用的音频格式

    public event Action<string> AudioSessionStartFailed;

    private bool _isInitialized;
    private bool _isAudioSessionStarted;

    public void Initialize(GameObject go)
    {
        try
        {
            _sipClient = go.GetComponent<SIPClient>();
            if (_sipClient == null)
            {
                UnityEngine.Debug.LogError("[AudioManager] 未找到 SIPClient 组件");
                return;
            }

            // 初始化音频编解码器管理器
            InitializeAudioCodecManagers();

            _isInitialized = true;
            UnityEngine.Debug.Log("[AudioManager] 音频管理器初始化完成");
        }
        catch (Exception ex)
        {
            UnityEngine.Debug.LogError($"[AudioManager] 初始化失败: {ex.Message}");
        }
    }

    private void InitializeAudioCodecManagers()
    {
        try
        {
            // 音频编解码器管理器初始化（使用官方 SIPSorceryMedia.Encoders）
            UnityEngine.Debug.Log("[AudioManager] 初始化音频编解码器管理器");
            
            // 设置默认音频格式为 G.722
            _audioFormat = new AudioFormat(SDPWellKnownMediaFormatsEnum.G722);
            UnityEngine.Debug.Log($"[AudioManager] 音频格式设置为: {_audioFormat.FormatID}");

            // 生成音频 SSRC
            _audioSsrc = (uint)new System.Random().Next();
            UnityEngine.Debug.Log($"[AudioManager] 音频 SSRC: {_audioSsrc}");

            UnityEngine.Debug.Log("[AudioManager] 音频编解码器管理器初始化完成");
        }
        catch (Exception ex)
        {
            UnityEngine.Debug.LogError($"[AudioManager] 音频编解码器管理器初始化失败: {ex.Message}");
        }
    }

    private void OnDestroy()
    {
        try
        {
            CleanupAudio();
        }
        catch (Exception ex)
        {
            UnityEngine.Debug.LogError($"[AudioManager] 清理音频资源时发生错误: {ex.Message}");
        }
    }

    private void CleanupAudio()
    {
        try
        {
            if (_audioSource != null)
            {
                _audioSource.OnAudioSourceEncodedSample -= OnAudioSourceEncodedSample;
                _audioSource = null;
            }

            if (_audioSink != null)
            {
                // 移除不存在的事件订阅
                _audioSink = null;
            }

            if (_mediaSession != null)
            {
                _mediaSession.OnRtpPacketReceived -= HandleRtpPacketReceived;
                _mediaSession.OnRtpEvent -= HandleRtpEvent;
                _mediaSession.OnTimeout -= HandleMediaSessionTimeout;
                _mediaSession = null;
            }

            _isAudioSessionStarted = false;
            UnityEngine.Debug.Log("[AudioManager] 音频资源清理完成");
        }
        catch (Exception ex)
        {
            UnityEngine.Debug.LogError($"[AudioManager] 清理音频资源失败: {ex.Message}");
        }
    }

    public async Task CloseAudioSession(string reason)
    {
        try
        {
            if (_mediaSession != null)
            {
                _mediaSession.Close(reason);
                _isAudioSessionStarted = false;
                UnityEngine.Debug.Log($"[AudioManager] 音频会话已关闭: {reason}");
            }
        }
        catch (Exception ex)
        {
            UnityEngine.Debug.LogError($"[AudioManager] 关闭音频会话失败: {ex.Message}");
        }
    }

    public VoIPMediaSession PrepareAudioSession()
    {
        try
        {
            if (_mediaSession != null)
            {
                return _mediaSession;
            }

            InitializeAudioSession();
            return _mediaSession;
        }
        catch (Exception ex)
        {
            UnityEngine.Debug.LogError($"[AudioManager] 准备音频会话失败: {ex.Message}");
            return null;
        }
    }

    public async Task StartAudioSession()
    {
        try
        {
            if (_isAudioSessionStarted)
            {
                UnityEngine.Debug.Log("[AudioManager] 音频会话已在运行");
                return;
            }

            if (_mediaSession == null)
            {
                InitializeAudioSession();
            }

            if (_mediaSession != null)
            {
                _mediaSession.Start();
                _isAudioSessionStarted = true;
                UnityEngine.Debug.Log("[AudioManager] 音频会话启动成功");
            }
        }
        catch (Exception ex)
        {
            UnityEngine.Debug.LogError($"[AudioManager] 启动音频会话失败: {ex.Message}");
            AudioSessionStartFailed?.Invoke(ex.Message);
        }
    }

    private void InitializeAudioSession()
    {
        try
        {
            // 创建音频源和接收器
            _audioSource = new AudioExtrasSource();

            // 修复：使用 AddComponent 创建 AudioExtrasSink，因为它是一个 MonoBehaviour
            var audioSinkComponent = gameObject.GetComponent<AudioExtrasSink>();
            if (audioSinkComponent == null)
            {
                audioSinkComponent = gameObject.AddComponent<AudioExtrasSink>();
            }
            _audioSink = audioSinkComponent;

            // 修复：获取或创建 AudioSource 组件并将其传递给 AudioExtrasSink
            var audioSourcePlayer = gameObject.GetComponent<AudioSource>();
            if (audioSourcePlayer == null)
            {
                audioSourcePlayer = gameObject.AddComponent<AudioSource>();
                UnityEngine.Debug.Log("[AudioManager] 已动态添加 AudioSource 组件用于播放。");
            }
            // 配置 AudioSource, 直接使用默认输出
            UnityEngine.Debug.Log("[AudioManager] AudioSource 将使用默认输出 (AudioListener)。");
            audioSinkComponent.SetUnityAudioSource(audioSourcePlayer);


            // 设置音频格式
            if (_audioSource is AudioExtrasSource audioSource)
            {
                audioSource.SetAudioSourceFormat(_audioFormat);
                audioSource.OnAudioSourceEncodedSample += OnAudioSourceEncodedSample;
            }

            if (_audioSink is AudioExtrasSink audioSink)
            {
                audioSink.SetAudioSinkFormat(_audioFormat);
            }

            // 创建媒体会话
            var mediaEndPoints = new MediaEndPoints
            {
                AudioSource = _audioSource,
                AudioSink = _audioSink
            };
            _mediaSession = new VoIPMediaSession(mediaEndPoints);
            _mediaSession.OnRtpPacketReceived += HandleRtpPacketReceived;
            _mediaSession.OnRtpEvent += HandleRtpEvent;
            _mediaSession.OnTimeout += HandleMediaSessionTimeout;

            UnityEngine.Debug.Log("[AudioManager] 音频会话初始化完成");
        }
        catch (Exception ex)
        {
            UnityEngine.Debug.LogError($"[AudioManager] 音频会话初始化失败: {ex.Message}");
        }
    }

    private void OnAudioSourceEncodedSample(uint timestamp, byte[] encodedSample)
    {
        try
        {
            // 🔥 详细的发送状态日志
            UnityEngine.Debug.Log($"[AudioManager] 接收到编码音频: {encodedSample.Length}字节, 时间戳: {timestamp}");
            UnityEngine.Debug.Log($"[AudioManager] 媒体会话状态: {_mediaSession != null}, 音频会话启动: {_isAudioSessionStarted}");

            if (_mediaSession != null && _isAudioSessionStarted)
            {
                _mediaSession.SendAudio(timestamp, encodedSample);
                UnityEngine.Debug.Log($"[AudioManager] ✅ 音频RTP包已发送: {encodedSample.Length}字节");
            }
            else
            {
                string reason = _mediaSession == null ? "媒体会话为空" : "音频会话未启动";
                UnityEngine.Debug.LogWarning($"[AudioManager] ❌ 无法发送音频: {reason}");
            }
        }
        catch (Exception ex)
        {
            UnityEngine.Debug.LogError($"[AudioManager] 发送音频帧失败: {ex.Message}");
        }
    }


    private void HandleMediaSessionTimeout(SDPMediaTypesEnum mediaType)
    {
        try
        {
            if (mediaType == SDPMediaTypesEnum.audio)
            {
                UnityEngine.Debug.LogWarning("[AudioManager] 音频会话超时");
                _isAudioSessionStarted = false;
            }
        }
        catch (Exception ex)
        {
            UnityEngine.Debug.LogError($"[AudioManager] 处理音频会话超时失败: {ex.Message}");
        }
    }

    private void HandleRtpEvent(IPEndPoint endPoint, RTPEvent rtpEvent, RTPHeader rtpHeader)
    {
        try
        {
            // 处理音频 RTP 事件
            if (rtpHeader.PayloadType == G722_PAYLOAD_TYPE || 
                rtpHeader.PayloadType == PCMU_PAYLOAD_TYPE || 
                rtpHeader.PayloadType == PCMA_PAYLOAD_TYPE)
            {
                _rtpArrivalStopwatch.Restart();
                // 音频 RTP 事件处理逻辑
            }
        }
        catch (Exception ex)
        {
            UnityEngine.Debug.LogError($"[AudioManager] 处理音频 RTP 事件失败: {ex.Message}");
        }
    }

    private void HandleRtpPacketReceived(IPEndPoint remoteEndPoint, SDPMediaTypesEnum mediaType, RTPPacket rtpPacket)
    {
        try
        {
            if (mediaType == SDPMediaTypesEnum.audio)
            {
                // 音频 RTP 包处理
                if (rtpPacket.Header.PayloadType == G722_PAYLOAD_TYPE || 
                    rtpPacket.Header.PayloadType == PCMU_PAYLOAD_TYPE || 
                    rtpPacket.Header.PayloadType == PCMA_PAYLOAD_TYPE)
                {
                    _rtpArrivalStopwatch.Stop();
                    var processingTime = _rtpArrivalStopwatch.ElapsedMilliseconds;
                    
                    if (processingTime > 50) // 如果处理时间超过50ms，记录警告
                    {
                        UnityEngine.Debug.LogWarning($"[AudioManager] 音频 RTP 包处理时间过长: {processingTime}ms");
                    }

                    // 音频包接收统计和处理
                    if (_audioSink != null)
                    {
                        _audioSink.GotAudioRtp(remoteEndPoint, rtpPacket.Header.SyncSource, rtpPacket.Header.SequenceNumber, rtpPacket.Header.Timestamp, rtpPacket.Header.PayloadType, rtpPacket.Header.MarkerBit == 1, rtpPacket.Payload);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            UnityEngine.Debug.LogError($"[AudioManager] 处理音频 RTP 包失败: {ex.Message}");
        }
    }

    public VoIPMediaSession GetAudioSession()
    {
        return _mediaSession;
    }

    public IAudioSource AudioSource { get { return _audioSource; } }

    public void SetRtpChannelReady(bool isReady)
    {
        _isRtpChannelReady = isReady;
        UnityEngine.Debug.Log($"[AudioManager] RTP 通道状态: {(isReady ? "就绪" : "未就绪")}");
    }

    public bool IsAudioSessionStarted()
    {
        return _isAudioSessionStarted;
    }

    public AudioFormat GetAudioFormat()
    {
        return _audioFormat;
    }

    public void SetAudioFormat(AudioFormat format)
    {
        _audioFormat = format;
        if (_audioSource is AudioExtrasSource audioSource)
        {
            audioSource.SetAudioSourceFormat(_audioFormat);
        }
        if (_audioSink is AudioExtrasSink audioSink)
        {
            audioSink.SetAudioSinkFormat(_audioFormat);
        }
        UnityEngine.Debug.Log($"[AudioManager] 音频格式已更新: {format.FormatID}");
    }
} 
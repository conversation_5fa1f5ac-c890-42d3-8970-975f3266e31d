using UnityEngine;
using System;
using System.Linq;
using SIPSorcery.Media;

/// <summary>
/// 音频发送诊断工具
/// 用于 检查SDP协商后为什么没有发送G722编码的RTP包
/// </summary>
public class AudioSendDiagnostic : MonoBehaviour
{
    [Header("诊断控制")]
    public KeyCode diagnoseAudioSendKey = KeyCode.F12;
    public KeyCode checkMicrophoneKey = KeyCode.F1;
    public KeyCode checkAudioSourceKey = KeyCode.F2;
    public KeyCode checkVADKey = KeyCode.F3;
    public KeyCode forceStartAudioKey = KeyCode.F4;
    public KeyCode checkRTPChannelKey = KeyCode.F5;
    
    [Header("诊断设置")]
    public bool enableDetailedLogs = true;
    public bool enableVADBypass = false; // 绕过VAD检测
    
    private AudioManager _audioManager;
    private LocalAudioExtrasSource _audioSource;
    private bool _isMonitoring = false;
    
    void Start()
    {
        Debug.Log("[AudioSendDiagnostic] 音频发送诊断工具已启动");
        Debug.Log($"  - 按 {diagnoseAudioSendKey} 进行完整音频发送诊断");
        Debug.Log($"  - 按 {checkMicrophoneKey} 检查麦克风状态");
        Debug.Log($"  - 按 {checkAudioSourceKey} 检查AudioSource状态");
        Debug.Log($"  - 按 {checkVADKey} 检查VAD语音检测");
        Debug.Log($"  - 按 {forceStartAudioKey} 强制启动音频采集");
        Debug.Log($"  - 按 {checkRTPChannelKey} 检查RTP通道状态");
    }
    
    void Update()
    {
        if (Input.GetKeyDown(diagnoseAudioSendKey))
        {
            DiagnoseAudioSend();
        }
        
        if (Input.GetKeyDown(checkMicrophoneKey))
        {
            CheckMicrophoneStatus();
        }
        
        if (Input.GetKeyDown(checkAudioSourceKey))
        {
            CheckAudioSourceStatus();
        }
        
        if (Input.GetKeyDown(checkVADKey))
        {
            CheckVADStatus();
        }
        
        if (Input.GetKeyDown(forceStartAudioKey))
        {
            ForceStartAudio();
        }
        
        if (Input.GetKeyDown(checkRTPChannelKey))
        {
            CheckRTPChannelStatus();
        }
    }
    
    /// <summary>
    /// 完整的音频发送诊断
    /// </summary>
    private void DiagnoseAudioSend()
    {
        Debug.Log("=== 开始音频发送诊断 ===");
        
        // 1. 查找组件
        FindAudioComponents();
        
        // 2. 检查SIP连接状态
        CheckSIPConnectionStatus();
        
        // 3. 检查音频会话状态
        CheckAudioSessionStatus();
        
        // 4. 检查麦克风状态
        CheckMicrophoneStatus();
        
        // 5. 检查AudioSource状态
        CheckAudioSourceStatus();
        
        // 6. 检查RTP通道状态
        CheckRTPChannelStatus();
        
        // 7. 检查事件订阅
        CheckEventSubscriptions();
        
        // 8. 检查VAD状态
        CheckVADStatus();
        
        Debug.Log("=== 音频发送诊断完成 ===");
    }
    
    /// <summary>
    /// 查找音频相关组件
    /// </summary>
    private void FindAudioComponents()
    {
        Debug.Log("--- 1. 查找音频组件 ---");
        
        _audioManager = FindObjectOfType<AudioManager>();
        if (_audioManager == null)
        {
            Debug.LogError("❌ 未找到AudioManager组件！");
            return;
        }
        Debug.Log("✅ 找到AudioManager组件");
        
        _audioSource = FindObjectOfType<LocalAudioExtrasSource>();
        if (_audioSource == null)
        {
            Debug.LogError("❌ 未找到LocalAudioExtrasSource组件！");
            return;
        }
        Debug.Log("✅ 找到LocalAudioExtrasSource组件");
    }
    
    /// <summary>
    /// 检查SIP连接状态
    /// </summary>
    private void CheckSIPConnectionStatus()
    {
        Debug.Log("--- 2. 检查SIP连接状态 ---");
        
        var sipClient = FindObjectOfType<SIPClient>();
        if (sipClient == null)
        {
            Debug.LogError("❌ 未找到SIPClient组件！");
            return;
        }
        
        // 通过反射或公共方法检查SIP状态
        Debug.Log("✅ SIPClient组件存在");
        // TODO: 添加更详细的SIP状态检查
    }
    
    /// <summary>
    /// 检查音频会话状态
    /// </summary>
    private void CheckAudioSessionStatus()
    {
        Debug.Log("--- 3. 检查音频会话状态 ---");
        
        if (_audioManager == null) return;
        
        bool isSessionStarted = _audioManager.IsAudioSessionStarted();
        Debug.Log($"音频会话启动状态: {isSessionStarted}");
        
        var mediaSession = _audioManager.GetAudioSession();
        if (mediaSession == null)
        {
            Debug.LogError("❌ 音频媒体会话为空！");
            return;
        }
        Debug.Log("✅ 音频媒体会话存在");
        
        // 检查媒体会话状态
        Debug.Log($"媒体会话已创建: {mediaSession != null}");
    }
    
    /// <summary>
    /// 检查麦克风状态
    /// </summary>
    private void CheckMicrophoneStatus()
    {
        Debug.Log("--- 4. 检查麦克风状态 ---");
        
        // 检查麦克风设备
        if (Microphone.devices.Length == 0)
        {
            Debug.LogError("❌ 未找到麦克风设备！");
            return;
        }
        
        Debug.Log($"✅ 找到 {Microphone.devices.Length} 个麦克风设备:");
        for (int i = 0; i < Microphone.devices.Length; i++)
        {
            string deviceName = Microphone.devices[i];
            bool isRecording = Microphone.IsRecording(deviceName);
            Debug.Log($"  - 设备 {i}: {deviceName} (录音中: {isRecording})");
            
            if (isRecording)
            {
                int position = Microphone.GetPosition(deviceName);
                Debug.Log($"    录音位置: {position}");
            }
        }
        
        // 检查默认麦克风
        string defaultMic = Microphone.devices[0];
        bool defaultIsRecording = Microphone.IsRecording(defaultMic);
        Debug.Log($"默认麦克风 '{defaultMic}' 录音状态: {defaultIsRecording}");
    }
    
    /// <summary>
    /// 检查AudioSource状态
    /// </summary>
    private void CheckAudioSourceStatus()
    {
        Debug.Log("--- 5. 检查AudioSource状态 ---");
        
        if (_audioSource == null) return;
        
        // 检查AudioSource是否启动
        Debug.Log($"AudioSource暂停状态: {_audioSource.IsAudioSourcePaused()}");
        Debug.Log($"AudioSource类型: {_audioSource.SourceType}");
        
        // 检查音频格式
        var formats = _audioSource.GetAudioSourceFormats();
        Debug.Log($"支持的音频格式数量: {formats.Count}");
        foreach (var format in formats)
        {
            Debug.Log($"  - 格式: {format.FormatName}, ID: {format.FormatID}, 采样率: {format.ClockRate}");
        }
        
        // 检查编码音频订阅者
        bool hasSubscribers = _audioSource.HasEncodedAudioSubscribers();
        Debug.Log($"编码音频订阅者存在: {hasSubscribers}");
    }
    
    /// <summary>
    /// 检查RTP通道状态
    /// </summary>
    private void CheckRTPChannelStatus()
    {
        Debug.Log("--- 6. 检查RTP通道状态 ---");
        
        if (_audioSource == null) return;
        
        // 检查RTP通道是否就绪
        // 注意：这需要AudioExtrasSource暴露RTP通道状态
        Debug.Log("RTP通道状态检查...");
        // TODO: 添加RTP通道状态检查逻辑
    }
    
    /// <summary>
    /// 检查事件订阅
    /// </summary>
    private void CheckEventSubscriptions()
    {
        Debug.Log("--- 7. 检查事件订阅 ---");
        
        if (_audioSource == null || _audioManager == null) return;
        
        // 检查OnAudioSourceEncodedSample事件是否被订阅
        bool hasEncodedSubscribers = _audioSource.HasEncodedAudioSubscribers();
        Debug.Log($"OnAudioSourceEncodedSample事件订阅状态: {hasEncodedSubscribers}");
        
        if (!hasEncodedSubscribers)
        {
            Debug.LogError("❌ 关键问题：OnAudioSourceEncodedSample事件没有订阅者！");
            Debug.LogError("   这意味着编码后的音频数据不会被发送");
        }
    }
    
    /// <summary>
    /// 检查VAD语音检测状态
    /// </summary>
    private void CheckVADStatus()
    {
        Debug.Log("--- 8. 检查VAD语音检测状态 ---");
        
        // 开始监控VAD状态
        if (!_isMonitoring)
        {
            StartCoroutine(MonitorVADStatus());
        }
        else
        {
            Debug.Log("VAD监控已在运行中...");
        }
    }
    
    /// <summary>
    /// 监控VAD状态
    /// </summary>
    private System.Collections.IEnumerator MonitorVADStatus()
    {
        _isMonitoring = true;
        Debug.Log("开始监控VAD状态（10秒）...");
        
        float startTime = Time.realtimeSinceStartup;
        while (Time.realtimeSinceStartup - startTime < 10f)
        {
            // 检查麦克风音量
            if (Microphone.devices.Length > 0 && Microphone.IsRecording(Microphone.devices[0]))
            {
                // 这里需要访问AudioExtrasSource的内部状态
                Debug.Log("VAD监控中... (请对着麦克风说话)");
            }
            
            yield return new WaitForSeconds(1f);
        }
        
        _isMonitoring = false;
        Debug.Log("VAD监控结束");
    }
    
    /// <summary>
    /// 强制启动音频采集
    /// </summary>
    private void ForceStartAudio()
    {
        Debug.Log("--- 强制启动音频采集 ---");
        
        if (_audioSource == null)
        {
            Debug.LogError("❌ AudioSource组件未找到");
            return;
        }

        try
        {
            _audioSource.Start();
            Debug.Log("✅ 已调用LocalAudioExtrasSource.Start()");
        }
        catch (Exception ex)
        {
            Debug.LogError($"❌ 强制启动音频失败: {ex.Message}");
        }
        
        if (_audioManager != null)
        {
            try
            {
                _audioManager.StartAudioSession();
                Debug.Log("✅ 已调用AudioManager.StartAudioSession()");
            }
            catch (Exception ex)
            {
                Debug.LogError($"❌ 启动音频会话失败: {ex.Message}");
            }
        }
    }
}

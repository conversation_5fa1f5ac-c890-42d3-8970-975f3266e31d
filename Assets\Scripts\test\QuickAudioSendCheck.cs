using UnityEngine;
using SIPSorcery.Media;

/// <summary>
/// 快速音频发送检查工具
/// 专门用于快速诊断为什么SDP协商后没有发送G722 RTP包
/// </summary>
public class QuickAudioSendCheck : MonoBehaviour
{
    [Header("快速检查")]
    public KeyCode quickCheckKey = KeyCode.F8;
    
    void Start()
    {
        Debug.Log("[QuickAudioSendCheck] 快速音频发送检查工具已启动");
        Debug.Log($"  - 按 {quickCheckKey} 进行快速音频发送检查");
    }
    
    void Update()
    {
        if (Input.GetKeyDown(quickCheckKey))
        {
            QuickCheck();
        }
    }
    
    /// <summary>
    /// 快速检查音频发送状态
    /// </summary>
    private void QuickCheck()
    {
        Debug.Log("=== 快速音频发送检查 ===");
        
        // 1. 检查麦克风
        CheckMicrophone();
        
        // 2. 检查AudioExtrasSource
        CheckAudioSource();
        
        // 3. 检查AudioManager
        CheckAudioManager();
        
        // 4. 检查事件订阅
        CheckEventSubscription();
        
        Debug.Log("=== 快速检查完成 ===");
        Debug.Log("💡 如果所有检查都通过但仍无RTP包，请检查VAD语音检测或网络连接");
    }
    
    /// <summary>
    /// 检查麦克风状态
    /// </summary>
    private void CheckMicrophone()
    {
        Debug.Log("--- 1. 麦克风检查 ---");
        
        if (Microphone.devices.Length == 0)
        {
            Debug.LogError("❌ 未找到麦克风设备！");
            return;
        }
        
        string defaultMic = Microphone.devices[0];
        bool isRecording = Microphone.IsRecording(defaultMic);
        
        Debug.Log($"✅ 麦克风设备: {defaultMic}");
        Debug.Log($"录音状态: {(isRecording ? "✅ 正在录音" : "❌ 未录音")}");
        
        if (isRecording)
        {
            int position = Microphone.GetPosition(defaultMic);
            Debug.Log($"录音位置: {position}");
        }
    }
    
    /// <summary>
    /// 检查AudioExtrasSource
    /// </summary>
    private void CheckAudioSource()
    {
        Debug.Log("--- 2. AudioExtrasSource检查 ---");
        
        var audioSource = FindObjectOfType<LocalAudioExtrasSource>();
        if (audioSource == null)
        {
            Debug.LogError("❌ 未找到LocalAudioExtrasSource组件！");
            return;
        }
        
        Debug.Log("✅ 找到LocalAudioExtrasSource组件");
        
        // 检查音频格式
        var formats = audioSource.GetAudioSourceFormats();
        Debug.Log($"支持的音频格式: {formats.Count}个");
        foreach (var format in formats)
        {
            Debug.Log($"  - {format.FormatName} (ID: {format.FormatID})");
        }
        
        // 检查编码订阅者
        bool hasSubscribers = audioSource.HasEncodedAudioSubscribers();
        Debug.Log($"编码音频订阅者: {(hasSubscribers ? "✅ 存在" : "❌ 不存在")}");
        
        if (!hasSubscribers)
        {
            Debug.LogError("❌ 关键问题：没有组件订阅编码音频事件！");
        }
    }
    
    /// <summary>
    /// 检查AudioManager
    /// </summary>
    private void CheckAudioManager()
    {
        Debug.Log("--- 3. AudioManager检查 ---");
        
        var audioManager = FindObjectOfType<AudioManager>();
        if (audioManager == null)
        {
            Debug.LogError("❌ 未找到AudioManager组件！");
            return;
        }
        
        Debug.Log("✅ 找到AudioManager组件");
        
        bool isSessionStarted = audioManager.IsAudioSessionStarted();
        Debug.Log($"音频会话状态: {(isSessionStarted ? "✅ 已启动" : "❌ 未启动")}");
        
        var mediaSession = audioManager.GetAudioSession();
        Debug.Log($"媒体会话: {(mediaSession != null ? "✅ 存在" : "❌ 不存在")}");
        
        if (!isSessionStarted)
        {
            Debug.LogWarning("⚠️ 音频会话未启动，尝试启动...");
            try
            {
                audioManager.StartAudioSession();
                Debug.Log("✅ 音频会话启动命令已发送");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ 启动音频会话失败: {ex.Message}");
            }
        }
    }
    
    /// <summary>
    /// 检查事件订阅关系
    /// </summary>
    private void CheckEventSubscription()
    {
        Debug.Log("--- 4. 事件订阅检查 ---");
        
        var audioSource = FindObjectOfType<LocalAudioExtrasSource>();
        var audioManager = FindObjectOfType<AudioManager>();
        
        if (audioSource == null || audioManager == null)
        {
            Debug.LogError("❌ 组件缺失，无法检查事件订阅");
            return;
        }
        
        // 检查是否有编码音频订阅者
        bool hasSubscribers = audioSource.HasEncodedAudioSubscribers();
        Debug.Log($"OnAudioSourceEncodedSample事件订阅: {(hasSubscribers ? "✅ 已订阅" : "❌ 未订阅")}");
        
        if (!hasSubscribers)
        {
            Debug.LogError("❌ 关键问题：AudioManager没有订阅AudioSource的编码音频事件！");
            Debug.LogError("   这意味着即使麦克风正常工作，编码后的音频也不会被发送");
            Debug.LogError("   请检查AudioManager.InitializeAudioSession()中的事件订阅代码");
        }
    }
    
    /// <summary>
    /// 在GUI中显示快速状态
    /// </summary>
    void OnGUI()
    {
        // 简单的状态显示
        GUILayout.BeginArea(new Rect(10, 10, 300, 150));
        GUILayout.BeginVertical("box");
        
        GUILayout.Label("快速音频发送状态");
        
        // 麦克风状态
        bool micOK = Microphone.devices.Length > 0 && Microphone.IsRecording(Microphone.devices[0]);
        GUILayout.Label($"🎤 麦克风: {(micOK ? "✅" : "❌")}");
        
        // AudioSource状态
        var audioSource = FindObjectOfType<LocalAudioExtrasSource>();
        bool sourceOK = audioSource != null && audioSource.HasEncodedAudioSubscribers();
        GUILayout.Label($"🔊 音频源: {(sourceOK ? "✅" : "❌")}");
        
        // AudioManager状态
        var audioManager = FindObjectOfType<AudioManager>();
        bool managerOK = audioManager != null && audioManager.IsAudioSessionStarted();
        GUILayout.Label($"📡 音频会话: {(managerOK ? "✅" : "❌")}");
        
        // 快速检查按钮
        if (GUILayout.Button($"快速检查 ({quickCheckKey})"))
        {
            QuickCheck();
        }
        
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
}

# Starting compiling UnityEngine.TestRunner.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-ffcf292aefd560d43b39777f7eb7db60
# Starting compiling UnityEngine.UI.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-6a6521285ea04c04faab867350266208
# Starting compiling UnityEditor.TestRunner.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-dd412a96a04b4244cb4ac7d469d6e03d
# Starting compiling UnityEditor.UI.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-63422816c4085fc41b7edd53ee332bb8
# Starting compiling Unity.VSCode.Editor.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-22ab067b89bb6474e85b100399e64a64
# Starting compiling Unity.VisualStudio.Editor.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-20c60eadd4c18a645a52168671b537a7
# Starting compiling Unity.Timeline.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-01081a7a31e94574aa055b2e0313dd62
# Starting compiling Unity.TextMeshPro.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-643c717c87700b4438840f0902485ebb
# Starting compiling Unity.PlasticSCM.Editor.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-8121507f85cdbc647a4859e500b26cd0
# Starting compiling Unity.Rider.Editor.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-06f07b4d1d5de994b9d02fec65a66c99
# Starting compiling Unity.SysrootPackage.Editor.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-a2883b633f58228418926dd9da9bb6a4
# Starting compiling Unity.TextMeshPro.Editor.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-df016d4161b550a48ba6c08b63ead539
# Starting compiling Unity.Sysroot.Linux_x86_64.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-006ada4585e7fb74799aab5c6dc45e39
# Starting compiling Unity.Timeline.Editor.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-211ea2eac55c15a4898250b4aa10fdbe
# Starting compiling Unity.CollabProxy.Editor.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-d286396cd07c62d4eac3dd39255365a7
# Starting compiling Unity.Toolchain.Win-x86_64-Linux-x86_64.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-927d57ce087508f4ea71acff7af18625
# Starting compiling Assembly-CSharp.dll
D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts\unity_csc.bat /noconfig @Temp/UnityTempFile-63c9c2c541797d645b486544e08f80d5

/*
 * 修改记录：
 * 2025-07-01: 将G.711音频编码器升级为G.722编码器
 * 原因：提升音频质量，从8kHz采样率升级到16kHz，提供更好的音频清晰度
 *
 * 【G.722编解码器升级】
 * - 将G.711 PCMU编码器替换为SIPSorcery的G.722编码器
 * - 采样率从8kHz(G.711)升级为16kHz(G.722)
 * - 帧大小调整为G.722标准参数：320样本(20ms @ 16kHz)
 * - 使用G722Codec和G722CodecState进行G.722编码
 * - RTP时钟速率保持8000Hz（G.722标准）
 * - Payload Type从0(PCMU)改为9(G722)
 *
 * 【音频质量提升】
 * - 16kHz采样率提供更好的音频频响范围
 * - G.722压缩算法在保持带宽效率的同时提升音质
 * - 更好的语音清晰度和自然度
 * - 与现代SIP设备的更好兼容性
 *
 * 【时序控制保持】
 * - 保持精确的20ms帧间隔控制
 * - RTP时间戳增量保持160（G.722使用8kHz RTP时钟）
 * - 维持稳定的发送时序，确保接收端播放流畅
 *
 * 【技术参数更新】
 * - 采样率：8kHz → 16kHz
 * - 帧大小：160样本 → 320样本
 * - 编码输出：160字节 → 160字节（G.722压缩比2:1）
 * - Payload Type：0 → 9
 * - 编解码器：MuLawEncoder → G722Codec
 *
 * 2025-06-30: 紧急修复RTP音频发送时序异常问题
 * 原因：Wireshark显示发送方向平均间隔34.15ms，抖动65306500ms，导致接收端音频杂音
 *
 * 【时序修复措施】
 * - 修复音频处理协程的时序控制逻辑
 * - 确保无论是否有足够音频数据，都保持精确20ms间隔
 * - 添加时序监控统计，每100帧输出平均间隔
 * - 在音频数据不足时仍推进时间戳，保持同步性
 *
 * 【问题分析】
 * - 原代码在音频数据不足时跳过处理但仍等待20ms，导致时序累积误差
 * - 缺乏时序监控，无法及时发现发送间隔异常
 * - 时间戳推进逻辑不完整，影响RTP包的时序连续性
 *
 * 2024-12-26: 音频编码和发送性能优化（配合AudioExtrasSink增强）
 *
 * 【编码性能优化】
 * - 保持高效的音频编码流程，确保20ms精确周期
 * - 优化VAD（语音活动检测）算法，减少不必要的编码
 * - 维持低延迟的音频采集和编码管道
 *
 * 【与接收器协同】
 * - 确保发送端时序精确，配合接收器的智能缓冲机制
 * - 提供稳定的RTP包发送，支持接收器的丢包检测
 * - 维持高质量的音频编码，为接收器提供最佳音频源
 */

using System;
using System.Collections.Generic;
using SIPSorceryMedia.Abstractions;
using UnityEngine;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SIPSorcery.Media
{
    /// <summary>
    /// 音频源类型枚举
    /// </summary>
    public enum AudioSourceType
    {
        /// <summary>麦克风输入</summary>
        Microphone,
        /// <summary>扬声器输出</summary>
        Speaker,
        /// <summary>文件输入</summary>
        File,
        /// <summary>网络输入</summary>
        Network
    }

    /// <summary>
    /// SIP音频源实现类
    /// 负责音频采集、编码和发送
    /// </summary>
    public class LocalAudioExtrasSource : MonoBehaviour, IAudioSource
    {
        private AudioSource _unityAudioSource;
        private SIPSorcery.Media.G722Codec _g722Encoder;
        private SIPSorcery.Media.G722CodecState _g722EncoderState;
        private bool _isStarted;
        private uint _audioTimestamp;
        private ushort _sequenceNumber;
        private AudioFormat _currentFormat;
        private bool _isFormatSet;
        private AudioSourceType _audioSourceType;
        private ConcurrentQueue<short[]> _audioQueue = new ConcurrentQueue<short[]>();
        private const int SAMPLE_RATE = 16000; // G.722采样率
        private const int BUFFER_SIZE = 320;   // 20ms 的音频数据 (16000 * 0.02)
        private const int MAX_QUEUE_SIZE = 10; // 最大缓冲队列大小
        private const float SILENCE_THRESHOLD = 0.01f; // 降低静音阈值，允许更低音量通过
        private const int MIN_SILENCE_DURATION = 5; // 增加静音判定时间，减少频繁切换
        public float NoiseGateThreshold = 0.005f; // 噪声门限，可从Unity Editor调整
        public float MicrophoneGain = 1.0f; // 麦克风增益控制，可从Unity Editor调整
        public float CompressionThreshold = 1.0f; // 压缩阈值，可从Unity Editor调整
        public float CompressionRatio = 1.0f; // 压缩比率，可从Unity Editor调整
        private float _lastAudioLevel = 0f;
        private AudioClip _microphoneClip;
        private float[] _microphoneBuffer = new float[BUFFER_SIZE];
        private int _lastPosition = 0;
        private bool _isRtpChannelReady = false;
        private bool _formatSetLogged = false; // 只在第一次设置格式时输出日志

        public AudioSource UnityAudioSource { get; set; }

        // 性能优化：预分配缓冲区以减少GC压力
        private byte[] _g722EncodeBuffer = new byte[BUFFER_SIZE / 2]; // G.722压缩比2:1，320样本->160字节
        private short[] _pcmProcessBuffer = new short[BUFFER_SIZE];
        private float[] _tempAudioBuffer = new float[BUFFER_SIZE];
        private readonly System.Random _random = new System.Random();
        private bool _isSpeaking = false; // VAD 状态：是否正在说话
        private int _silenceDurationCounter = 0; // 静默持续时间计数器

        public event SourceErrorDelegate OnError;
        public event EncodedSampleDelegate OnAudioSourceEncodedSample;
        public event SourceErrorDelegate OnAudioSourceError;
        public event RawAudioSampleDelegate OnAudioSourceRawSample; // 重新添加此行
        public event Action<bool> OnRtpChannelStateChanged;

        public AudioSourceType SourceType
        {
            get => _audioSourceType;
            set
            {
                _audioSourceType = value;
                UnityEngine.Debug.Log($"[LocalAudioExtrasSource] 设置音频源类型: {value}");
            }
        }

        public bool IsFormatSet => _isFormatSet;

        private void Awake()
        {
            _unityAudioSource = GetComponent<AudioSource>();
            if (_unityAudioSource == null)
            {
                UnityEngine.Debug.LogError("[LocalAudioExtrasSource] AudioSource 组件未找到!");
            }

            // 初始化RTP参数
            _sequenceNumber = (ushort)_random.Next(ushort.MaxValue);
            _audioTimestamp = (uint)_random.Next();

            // 初始化G.722编码器
            _g722Encoder = new SIPSorcery.Media.G722Codec();
            _g722EncoderState = new SIPSorcery.Media.G722CodecState(64000, SIPSorcery.Media.G722Flags.None); // 64kbit/s

            // 设置默认音频格式为G.722
            try
            {
                var defaultFormat = new AudioFormat(SDPWellKnownMediaFormatsEnum.G722);
                SetAudioSourceFormat(defaultFormat);
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"[AudioExtrasSource] Failed to set default audio format: {ex.Message}");
            }

            UnityEngine.Debug.Log("[LocalAudioExtrasSource] G.722初始化完成");
        }

        private void InitializeMicrophone()
        {
            UnityEngine.Debug.Log("[LocalAudioExtrasSource] 开始初始化麦克风...");
            UnityMainThreadDispatcher.Instance().Enqueue(() =>
            {
                if (Microphone.devices.Length == 0)
                {
                    UnityEngine.Debug.LogError("[LocalAudioExtrasSource] 未找到麦克风设备");
                    return;
                }
                try
                {
                    _microphoneClip = Microphone.Start(Microphone.devices[0], true, 1, SAMPLE_RATE);
                    if (_microphoneClip == null)
                    {
                        throw new Exception("无法启动麦克风");
                    }
                    UnityEngine.Debug.Log("[LocalAudioExtrasSource] 麦克风初始化成功");
                }
                catch (Exception ex)
                {
                    UnityEngine.Debug.LogError($"[LocalAudioExtrasSource] 初始化麦克风失败: {ex.Message}");
                    throw;
                }
            });
        }

        public void SetRtpChannelReady(bool isReady)
        {
            _isRtpChannelReady = isReady;
            UnityEngine.Debug.Log($"[LocalAudioExtrasSource] RTP通道状态已更新: {isReady}");
            OnRtpChannelStateChanged?.Invoke(isReady);
            // RTP通道ready后自动启动音频采集
            if (isReady && !_isStarted)
            {
                Start();
            }
        }

        private void StartAudioProcessing()
        {
            UnityEngine.Debug.Log($"[LocalAudioExtrasSource] StartAudioProcessing 协程启动. _isStarted: {_isStarted}");
            StartCoroutine(AudioProcessingCoroutine());
        }

        /// <summary>
        /// 使用Unity协程实现精确的20ms音频处理周期
        /// </summary>
        private System.Collections.IEnumerator AudioProcessingCoroutine()
        {
            UnityEngine.Debug.Log("[LocalAudioExtrasSource] 音频处理协程已启动，使用精确20ms时序");

            int expectedStep = BUFFER_SIZE;

            // 修复：使用高精度时序控制
            float targetInterval = 0.02f; // 20ms目标间隔
            float nextProcessTime = Time.realtimeSinceStartup + targetInterval;

            // 修复：正确的时序监控
            float lastStatTime = Time.realtimeSinceStartup;
            int processedFrames = 0;

            while (true)
            {
                float currentTime = Time.realtimeSinceStartup;

                if (_isStarted && _microphoneClip != null)
                {
                    int currentPosition = Microphone.GetPosition(Microphone.devices[0]);
                    if (currentPosition < _lastPosition)
                        _lastPosition = 0;
                    int available = currentPosition - _lastPosition;
                    if (available < 0) available += _microphoneClip.samples;

                    if (available >= expectedStep)
                    {
                        _microphoneClip.GetData(_microphoneBuffer, _lastPosition);
                        // 应用噪声门限和增益控制
                        float maxLevel = 0;
                        for (int i = 0; i < BUFFER_SIZE; i++)
                        {
                            float sample = _microphoneBuffer[i];
                            float sampleAbs = Math.Abs(sample);

                            // 动态噪声门限
                            if (sampleAbs < NoiseGateThreshold)
                            {
                                // 使用软噪声门限，而不是直接设为0
                                sample *= (sampleAbs / NoiseGateThreshold);
                            }

                            // 应用压缩和增益
                            if (sampleAbs > CompressionThreshold)
                            {
                                // 对超过阈值的部分应用压缩
                                float excess = sampleAbs - CompressionThreshold;
                                float compressed = CompressionThreshold + (excess * CompressionRatio);
                                sample = (sample > 0 ? compressed : -compressed);
                            }

                            // 应用增益
                            sample *= MicrophoneGain;

                            // 软限幅，使用平滑过渡
                            if (sampleAbs > 0.95f)
                            {
                                float excess = sampleAbs - 0.95f;
                                float reduction = 1.0f - (excess * 10f); // 在0.95到1.0之间平滑过渡
                                reduction = Mathf.Clamp01(reduction);
                                sample *= reduction;
                            }

                            _microphoneBuffer[i] = sample;
                            maxLevel = Mathf.Max(maxLevel, sampleAbs);
                        }

                        // 将float音频数据转换为PCM short数据
                        short[] pcmData = new short[BUFFER_SIZE];
                        float pcmMaxAbs = 0f;
                        for (int i = 0; i < BUFFER_SIZE; i++)
                        {
                            pcmData[i] = (short)(_microphoneBuffer[i] * 32767f);
                            pcmMaxAbs = Mathf.Max(pcmMaxAbs, Mathf.Abs(pcmData[i] / 32767f));
                        }

                        // 语音活动检测 (VAD) 逻辑
                        bool currentIsSpeaking = maxLevel > SILENCE_THRESHOLD;

                        if (currentIsSpeaking)
                        {
                            _isSpeaking = true;
                            _silenceDurationCounter = 0;
                        }
                        else
                        {
                            _silenceDurationCounter++;
                            if (_silenceDurationCounter >= MIN_SILENCE_DURATION)
                            {
                                _isSpeaking = false;
                            }
                        }

                        // [VAD 诊断日志]
                        UnityEngine.Debug.Log($"[VAD Check] maxLevel: {maxLevel:F4}, SILENCE_THRESHOLD: {SILENCE_THRESHOLD:F4}, isSpeaking: {_isSpeaking}");

                        // 🔥 临时禁用VAD进行测试 - 总是发送音频
                        bool shouldSendAudio = true; // 改为 _isSpeaking 来恢复VAD

                        if (shouldSendAudio)
                        {
                            // 1. 执行G.722编码
                            int encodedBytes = _g722Encoder.Encode(_g722EncoderState, _g722EncodeBuffer, pcmData, pcmData.Length);

                            if (encodedBytes > 0)
                            {
                                byte[] encodedSample = new byte[encodedBytes];
                                Buffer.BlockCopy(_g722EncodeBuffer, 0, encodedSample, 0, encodedBytes);

                                // 🔥 详细的发送日志
                                UnityEngine.Debug.Log($"[AudioSend] 编码成功: {encodedBytes}字节, 时间戳: {_audioTimestamp}, 订阅者: {OnAudioSourceEncodedSample != null}");

                                // 2. 触发正确的、携带编码后数据的事件
                                OnAudioSourceEncodedSample?.Invoke(_audioTimestamp, encodedSample);

                                // 🔥 确认事件已触发
                                if (OnAudioSourceEncodedSample != null)
                                {
                                    UnityEngine.Debug.Log($"[AudioSend] ✅ 音频数据已发送到 {OnAudioSourceEncodedSample.GetInvocationList().Length} 个订阅者");
                                }
                                else
                                {
                                    UnityEngine.Debug.LogError("[AudioSend] ❌ 没有订阅者接收编码音频数据！");
                                }
                            }
                            else
                            {
                                UnityEngine.Debug.LogError($"[AudioSend] ❌ G722编码失败: 输入{pcmData.Length}样本, 输出{encodedBytes}字节");
                            }

                            // 3. 推进RTP时间戳
                            _audioTimestamp += 160; // G.722 RTP时钟速率为8000Hz，20ms音频 = 160个时钟周期
                        }
                        else
                        {
                            // 如果不说话，时间戳仍然需要前进，以保持同步性
                            _audioTimestamp += 160;
                            //Debug.Log($"[LocalAudioExtrasSource] 检测到静默，未发送RTP包。当前时间戳: {_audioTimestamp}");
                        }

                        _lastPosition = (_lastPosition + expectedStep) % _microphoneClip.samples;
                        processedFrames++;

                        // 修复：每100帧输出一次正确的时序统计
                        if (processedFrames % 100 == 0)
                        {
                            float totalTime = currentTime - lastStatTime;
                            float avgInterval = (totalTime / 100f) * 1000f; // 修复：正确计算平均间隔
                            UnityEngine.Debug.Log($"[LocalAudioExtrasSource] 时序统计 - 平均间隔: {avgInterval:F2}ms, 目标: 20ms");
                            lastStatTime = currentTime;
                        }
                    }
                    else
                    {
                        // 如果音频数据不足，仍然需要推进时间戳以保持同步
                        _audioTimestamp += 160;
                        //UnityEngine.Debug.LogWarning($"[LocalAudioExtrasSource] 音频数据不足，跳过处理但保持时序同步");
                    }

                    // 修复：使用精确的时序控制，而不是固定等待时间
                    while (Time.realtimeSinceStartup < nextProcessTime)
                    {
                        yield return null; // 等待到精确的处理时间
                    }
                    nextProcessTime += targetInterval; // 设置下一次处理时间
                }
                else
                {
                    // 如果未启动，等待100ms后重试
                    yield return new WaitForSecondsRealtime(0.1f);
                }
            }
        }

        // 移除 InitializeAudioEncoders 方法，因为我们直接使用 G711编码器
        // private void InitializeAudioEncoders(AudioFormat format)
        // {
        //     _audioEncoders.Clear();
        //
        //     try
        //     {
        //         // 根据音频格式创建相应的编码器
        //         if (format.FormatName == SDPWellKnownMediaFormatsEnum.G722.ToString())
        //         {
        //             // 使用 SIPSorcery 的音频编码器
        //             var audioEncoder = new AudioEncoder(true, true); // 包含线性格式
        //             _audioEncoders.Add(audioEncoder);
        //         }
        //         else
        //         {
        //             throw new NotSupportedException($"不支持的音频格式: {format.FormatName}");
        //         }
        //
        //         _isEncoderInitialized = true;
        //         if (!_formatSetLogged) {
        //             Debug.Log($"[LocalAudioExtrasSource] 音频编码器初始化完成，编码器数量: {_audioEncoders.Count}");
        //         }
        //     }
        //     catch (Exception ex)
        //     {
        //         Debug.LogError($"[LocalAudioExtrasSource] 初始化音频编码器失败: {ex.Message}");
        //         _isEncoderInitialized = false;
        //         throw;
        //     }
        // }

        public void SetAudioSourceFormat(AudioFormat audioFormat)
        {
            string formatName = object.ReferenceEquals(audioFormat, null) ? "null" : audioFormat.FormatName;

            if (object.ReferenceEquals(audioFormat, null))
            {
                UnityEngine.Debug.LogError("[LocalAudioExtrasSource] 音频格式为空");
                _isFormatSet = false;
                return;
            }

            try
            {
                // 验证音频格式
                if (audioFormat.FormatName != SDPWellKnownMediaFormatsEnum.G722.ToString())
                {
                    UnityEngine.Debug.LogError($"[AudioExtrasSource] 不支持的音频格式: {audioFormat.FormatName}");
                    _isFormatSet = false;
                    return;
                }

                // 设置音频格式
                _currentFormat = audioFormat;
                _isFormatSet = true;

                // 配置音频源（主线程）- 使用Unity系统采样率避免影响其他音频
                if (_unityAudioSource != null)
                {
                    UnityMainThreadDispatcher.Instance().Enqueue(() =>
                    {
                        int unitySampleRate = AudioSettings.outputSampleRate;
                        _unityAudioSource.clip = AudioClip.Create("SIPAudio", unitySampleRate, 1, unitySampleRate, false);
                        _unityAudioSource.loop = false;
                        UnityEngine.Debug.Log($"[AudioExtrasSource] AudioClip创建 - Unity采样率: {unitySampleRate}Hz (避免使用RTP的16kHz)");
                    });
                }

                if (!_formatSetLogged)
                {
                    UnityEngine.Debug.Log($"[LocalAudioExtrasSource] 音频格式设置成功: {audioFormat.FormatName}");
                    _formatSetLogged = true;
                }
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"[LocalAudioExtrasSource] 设置音频格式时发生错误: {ex.Message}");
                _isFormatSet = false;
                throw;
            }
        }

        // 移除 SetAudioSourceFormat(IAudioEncoder audioEncoder) 和 SetAudioSourceFormat(List<IAudioEncoder> audioEncoders)
        // 因为我们直接使用 G711编码器，不再需要管理 IAudioEncoder 列表
        // public void SetAudioSourceFormat(IAudioEncoder audioEncoder)
        // {
        //     if (audioEncoder == null)
        //     {
        //         throw new ArgumentNullException(nameof(audioEncoder), "Audio encoder cannot be null");
        //     }
        //     if (!_audioEncoders.Contains(audioEncoder))
        //     {
        //         _audioEncoders.Add(audioEncoder);
        //         Debug.Log($"[AudioSource] Added audio encoder: {audioEncoder.GetType().Name}. Total encoders: {_audioEncoders.Count}");
        //     }
        // }
        //
        // public void SetAudioSourceFormat(List<IAudioEncoder> audioEncoders)
        // {
        //     if (audioEncoders == null)
        //     {
        //         throw new ArgumentNullException(nameof(audioEncoders), "Audio encoders list cannot be null");
        //     }
        //     _audioEncoders.Clear();
        //     _audioEncoders.AddRange(audioEncoders);
        //     Debug.Log($"[AudioSource] Set audio encoders list. Count: {audioEncoders.Count}");
        // }

        public void RestrictFormats(Func<AudioFormat, bool> filter) { }

        public List<AudioFormat> GetAudioSourceFormats() => new List<AudioFormat>
        {
            // 返回G.722格式
            new AudioFormat(SDPWellKnownMediaFormatsEnum.G722)
        };

        public void Start()
        {
            UnityEngine.Debug.Log("[LocalAudioExtrasSource] Start started.");
            if (!_isStarted)
            {
                if (!_isFormatSet)
                {
                    UnityEngine.Debug.LogError("[LocalAudioExtrasSource] 音频格式未设置，无法启动音频源");
                    return;
                }
                if (SourceType == AudioSourceType.Microphone)
                {
                    try
                    {
                        InitializeMicrophone();
                        _isStarted = true;
                        StartAudioProcessing();
                        UnityEngine.Debug.Log("[LocalAudioExtrasSource] 音频源启动成功 (麦克风)");
                    }
                    catch (Exception ex)
                    {
                        UnityEngine.Debug.LogError($"[LocalAudioExtrasSource] 启动麦克风失败: {ex.Message}");
                        OnError?.Invoke($"启动麦克风失败: {ex.Message}");
                    }
                }
                else
                {
                    UnityEngine.Debug.LogWarning($"[LocalAudioExtrasSource] 音频源类型 {SourceType} 尚未实现");
                }
            }
            else
            {
                UnityEngine.Debug.LogWarning("[LocalAudioExtrasSource] 音频源已经启动");
            }
            UnityEngine.Debug.Log("[LocalAudioExtrasSource] Start finished.");
        }

        public void Stop()
        {
            UnityEngine.Debug.Log("[LocalAudioExtrasSource] Stop started.");
            if (_isStarted)
            {
                _isStarted = false;
                if (SourceType == AudioSourceType.Microphone)
                {
                    UnityMainThreadDispatcher.Instance().Enqueue(() =>
                    {
                        try
                        {
                            if (Microphone.devices.Length > 0)
                            {
                                string micName = Microphone.devices[0];
                                if (_microphoneClip != null && Microphone.IsRecording(micName))
                                {
                                    Microphone.End(micName);
                                    UnityEngine.Debug.Log("[LocalAudioExtrasSource] 麦克风已停止");
                                }
                                else
                                {
                                    UnityEngine.Debug.LogWarning("[LocalAudioExtrasSource] 麦克风未在录制或设备不可用，无需停止。");
                                }
                            }
                            else
                            {
                                UnityEngine.Debug.LogWarning("[LocalAudioExtrasSource] 没有可用的麦克风设备。");
                            }
                        }
                        catch (Exception ex)
                        {
                            UnityEngine.Debug.LogError($"[LocalAudioExtrasSource] 停止麦克风失败: {ex.Message}");
                            OnError?.Invoke($"停止麦克风失败: {ex.Message}");
                        }
                    });
                }
                else
                {
                    UnityEngine.Debug.LogWarning($"[LocalAudioExtrasSource] 音频源类型 {SourceType} 尚未实现");
                }
            }
            else
            {
                UnityEngine.Debug.LogWarning("[LocalAudioExtrasSource] 音频源未启动");
            }
            UnityEngine.Debug.Log("[LocalAudioExtrasSource] Stop finished.");
        }

        /// <summary>
        /// 将16位线性PCM样本转换为8位Mu-Law样本。
        /// </summary>
        /// <param name="pcmSample">16位线性PCM样本。</param>
        /// <returns>8位Mu-Law样本。</returns>
        private byte LinearToMuLaw(short pcmSample)
        {
            const int BIAS = 0x84;
            int sign = (pcmSample >> 8) & 0x80;
            if (sign != 0) pcmSample = (short)-pcmSample;
            int segment = Clip(pcmSample >> 4, 0, 7);
            int muLaw = sign | segment | ((pcmSample >> (segment + 3)) & 0x0F);
            return (byte)(muLaw ^ BIAS);
        }

        /// <summary>
        /// 限制输入值在指定范围内。
        /// </summary>
        /// <param name="value">要限制的值。</param>
        /// <param name="min">最小值。</param>
        /// <param name="max">最大值。</param>
        /// <returns>限制后的值。</returns>
        private int Clip(int value, int min, int max)
        {
            if (value < min) return min;
            if (value > max) return max;
            return value;
        }

        public Task PauseAudio()
        {
            UnityEngine.Debug.Log("[LocalAudioExtrasSource] PauseAudio called.");
            if (_unityAudioSource != null && _unityAudioSource.isPlaying)
            {
                _unityAudioSource.Pause();
            }

            return Task.CompletedTask;
        }

        public Task ResumeAudio()
        {
            UnityEngine.Debug.Log("[LocalAudioExtrasSource] ResumeAudio called.");
            if (_unityAudioSource != null && !_unityAudioSource.isPlaying)
            {
                _unityAudioSource.UnPause();
            }

            return Task.CompletedTask;
        }

        public Task StartAudio()
        {
            UnityEngine.Debug.Log("[LocalAudioExtrasSource] StartAudio called.");
            Start();

            return Task.CompletedTask;
        }

        public Task CloseAudio()
        {
            UnityEngine.Debug.Log("[LocalAudioExtrasSource] CloseAudio called.");
            Stop();

            return Task.CompletedTask;
        }

        public void ExternalAudioSourceRawSample(AudioSamplingRatesEnum sampleRate, uint durationMilliseconds, short[] sample)
        {
            UnityEngine.Debug.Log($"[LocalAudioExtrasSource] ExternalAudioSourceRawSample called with sampleRate: {sampleRate}, durationMilliseconds: {durationMilliseconds}, sample length: {sample.Length}");
            // 实现外部音频源原始样本处理逻辑
            if (_unityAudioSource != null)
            {
                // 将外部音频数据写入 Unity AudioSource
                float[] floatArray = Array.ConvertAll(sample, s => (float)s / short.MaxValue);
                AudioClip clip = AudioClip.Create("ExternalAudio", sample.Length, 1, (int)sampleRate, false);
                clip.SetData(floatArray, 0);
                _unityAudioSource.clip = clip;
                _unityAudioSource.Play();
            }
        }

        public bool HasEncodedAudioSubscribers()
        {
            // 检查是否有编码音频订阅者
            return OnAudioSourceEncodedSample != null;
        }

        public bool IsAudioSourcePaused()
        {
            // 检查 Unity AudioSource 是否正在播放
            return _unityAudioSource != null && !_unityAudioSource.isPlaying;
        }

    }
}

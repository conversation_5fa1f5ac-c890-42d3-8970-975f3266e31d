# 音频发送问题诊断指南

## 📅 创建日期：2025-07-16

## 🎯 问题描述
SDP协商成功后，网络抓包显示没有发送麦克风G722编码的RTP包。

## 🔍 可能原因分析

### 1. **VAD（语音活动检测）阻止发送**
**现象**：麦克风正常，但没有检测到语音活动
**原因**：VAD阈值过高，或环境太安静
**解决**：临时禁用VAD或降低阈值

### 2. **麦克风未正确启动**
**现象**：麦克风权限问题或设备问题
**原因**：Unity无法访问麦克风或麦克风被其他应用占用
**解决**：检查麦克风权限和设备状态

### 3. **AudioSource未启动**
**现象**：LocalAudioExtrasSource组件存在但未启动
**原因**：`_isStarted`标志为false或启动失败
**解决**：手动调用Start()方法

### 4. **事件订阅缺失**
**现象**：音频编码成功但没有发送
**原因**：AudioManager没有订阅OnAudioSourceEncodedSample事件
**解决**：检查事件订阅代码

### 5. **音频会话未启动**
**现象**：媒体会话存在但未启动
**原因**：StartAudioSession()未调用或失败
**解决**：手动启动音频会话

### 6. **LocalAudioExtrasSource组件未创建**
**现象**：❌ 未找到LocalAudioExtrasSource组件！
**原因**：AudioManager未正确初始化或组件创建失败
**解决**：检查SIPClientSceneSetup和AudioManager初始化

## 🛠️ 诊断工具

### 1. ComponentVerifier.cs（首先使用）
**用途**：验证SIP组件是否正确创建
**使用**：
```csharp
// 添加到场景中的任意GameObject
// 按F6验证所有组件
// 查看右上角GUI状态显示
```

**检查项目**：
- ✅ SIPClient组件存在
- ✅ MediaManager组件存在
- ✅ AudioManager组件存在
- ✅ LocalAudioExtrasSource组件存在
- ✅ AudioExtrasSink组件存在

### 2. QuickAudioSendCheck.cs（推荐）
**用途**：快速检查音频发送状态
**使用**：
```csharp
// 添加到场景中的任意GameObject
// 按F8进行快速检查
// 查看GUI状态显示
```

**检查项目**：
- ✅ 麦克风录音状态
- ✅ AudioSource组件和订阅者
- ✅ AudioManager会话状态
- ✅ 事件订阅关系

### 3. AudioSendDiagnostic.cs（详细）
**用途**：完整的音频发送诊断
**使用**：
```csharp
// 按F12进行完整诊断
// 按F1-F5进行分项检查
```

## 🚀 快速修复步骤

### 步骤1：验证组件创建
```csharp
// 添加ComponentVerifier.cs到场景
// 按F6验证所有组件
// 确保所有组件都显示✅状态
```

### 步骤2：运行快速检查
```csharp
// 添加QuickAudioSendCheck.cs到场景
// 按F8查看状态
// 观察GUI中的✅❌状态指示
```

### 步骤3：检查关键日志
查找以下日志信息：
```
✅ 正常日志：
[LocalAudioExtrasSource] 音频源启动成功 (麦克风)
[AudioSend] 编码成功: 160字节, 时间戳: xxx
[AudioManager] ✅ 音频RTP包已发送: 160字节

❌ 问题日志：
[AudioSend] ❌ 没有订阅者接收编码音频数据！
[AudioManager] ❌ 无法发送音频: 音频会话未启动
```

### 步骤4：临时禁用VAD测试
如果其他都正常但仍无RTP包，VAD可能阻止了发送：
```csharp
// 在AudioExtrasSource.cs中已临时禁用VAD
// 查找：bool shouldSendAudio = true; // 改为 _isSpeaking 来恢复VAD
```

### 步骤5：强制启动音频
```csharp
// 按F4强制启动音频采集
// 或在代码中手动调用：
audioSource.Start();
audioManager.StartAudioSession();
```

## 📊 诊断流程图

```
开始诊断
    ↓
检查麦克风 → ❌ 修复麦克风权限/设备
    ↓ ✅
检查AudioSource → ❌ 调用Start()启动
    ↓ ✅  
检查事件订阅 → ❌ 修复事件订阅代码
    ↓ ✅
检查音频会话 → ❌ 启动音频会话
    ↓ ✅
检查VAD状态 → ❌ 禁用VAD或降低阈值
    ↓ ✅
应该能看到RTP包
```

## 🔧 代码修复

### 修复1：确保事件订阅
```csharp
// 在AudioManager.InitializeAudioSession()中
if (_audioSource is LocalAudioExtrasSource audioSource)
{
    audioSource.OnAudioSourceEncodedSample += OnAudioSourceEncodedSample;
    Debug.Log("✅ 已订阅编码音频事件");
}
```

### 修复2：临时禁用VAD
```csharp
// 在AudioExtrasSource.cs中
bool shouldSendAudio = true; // 临时禁用VAD
// bool shouldSendAudio = _isSpeaking; // 恢复VAD

if (shouldSendAudio) {
    // 编码和发送音频
}
```

### 修复3：修复组件创建问题
```csharp
// 在AudioManager.InitializeAudioSession()中
// 修复：使用AddComponent创建LocalAudioExtrasSource
var audioSourceComponent = gameObject.GetComponent<LocalAudioExtrasSource>();
if (audioSourceComponent == null)
{
    audioSourceComponent = gameObject.AddComponent<LocalAudioExtrasSource>();
    Debug.Log("[AudioManager] 已动态添加 LocalAudioExtrasSource 组件。");
}
_audioSource = audioSourceComponent;
```

### 修复4：强制启动音频
```csharp
// 确保音频组件启动
public void ForceStartAudio()
{
    var audioSource = FindObjectOfType<LocalAudioExtrasSource>();
    var audioManager = FindObjectOfType<AudioManager>();

    audioSource?.Start();
    audioManager?.StartAudioSession();
}
```

## 📋 检查清单

### 基础检查
- [ ] 麦克风设备存在且可访问
- [ ] LocalAudioExtrasSource组件存在
- [ ] AudioManager组件存在
- [ ] SIP连接已建立

### 状态检查
- [ ] 麦克风正在录音
- [ ] AudioSource已启动（_isStarted = true）
- [ ] 音频会话已启动
- [ ] 媒体会话存在且有效

### 事件检查
- [ ] OnAudioSourceEncodedSample事件有订阅者
- [ ] AudioManager订阅了编码音频事件
- [ ] 事件触发时有日志输出

### 数据流检查
- [ ] 麦克风数据采集正常
- [ ] G722编码成功
- [ ] 编码音频事件触发
- [ ] RTP包发送成功

## 🎯 预期结果

修复后，您应该看到：

### Console日志
```
[LocalAudioExtrasSource] 音频源启动成功 (麦克风)
[VAD Check] maxLevel: 0.xxxx, isSpeaking: true
[AudioSend] 编码成功: 160字节, 时间戳: xxx, 订阅者: True
[AudioSend] ✅ 音频数据已发送到 1 个订阅者
[AudioManager] 接收到编码音频: 160字节, 时间戳: xxx
[AudioManager] ✅ 音频RTP包已发送: 160字节
```

### 网络抓包
```
RTP包特征：
- 协议：RTP
- 载荷类型：9 (G722)
- 包大小：约172字节 (12字节RTP头 + 160字节G722数据)
- 发送间隔：约20ms
- 目标端口：SDP协商的音频端口
```

### GUI状态
```
🎤 麦克风: ✅
🔊 音频源: ✅  
📡 音频会话: ✅
```

## ⚠️ 注意事项

1. **VAD敏感性**：在安静环境中，VAD可能检测不到语音
2. **麦克风权限**：确保Unity有麦克风访问权限
3. **设备占用**：确保麦克风没有被其他应用占用
4. **网络连接**：确保RTP通道已建立
5. **时序问题**：音频会话必须在SDP协商完成后启动

使用这些工具和步骤，您应该能够快速定位并解决音频发送问题！

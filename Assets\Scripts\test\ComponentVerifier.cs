using UnityEngine;
using SIPSorcery.Media;

/// <summary>
/// 组件验证工具
/// 用于验证SIP相关组件是否正确创建和初始化
/// </summary>
public class ComponentVerifier : MonoBehaviour
{
    [Header("验证控制")]
    public KeyCode verifyComponentsKey = KeyCode.F6;
    
    void Start()
    {
        Debug.Log("[ComponentVerifier] 组件验证工具已启动");
        Debug.Log($"  - 按 {verifyComponentsKey} 验证所有SIP组件");
    }
    
    void Update()
    {
        if (Input.GetKeyDown(verifyComponentsKey))
        {
            VerifyAllComponents();
        }
    }
    
    /// <summary>
    /// 验证所有SIP相关组件
    /// </summary>
    private void VerifyAllComponents()
    {
        Debug.Log("=== SIP组件验证开始 ===");
        
        // 1. 验证SIPClient
        VerifySIPClient();
        
        // 2. 验证MediaManager
        VerifyMediaManager();
        
        // 3. 验证AudioManager
        VerifyAudioManager();
        
        // 4. 验证VideoManager
        VerifyVideoManager();
        
        // 5. 验证LocalAudioExtrasSource
        VerifyLocalAudioExtrasSource();
        
        // 6. 验证AudioExtrasSink
        VerifyAudioExtrasSink();
        
        Debug.Log("=== SIP组件验证完成 ===");
    }
    
    private void VerifySIPClient()
    {
        Debug.Log("--- 验证SIPClient ---");
        var sipClient = FindObjectOfType<SIPClient>();
        if (sipClient != null)
        {
            Debug.Log($"✅ SIPClient: {sipClient.gameObject.name}");
        }
        else
        {
            Debug.LogError("❌ 未找到SIPClient组件");
        }
    }
    
    private void VerifyMediaManager()
    {
        Debug.Log("--- 验证MediaManager ---");
        var mediaManager = FindObjectOfType<SIPSorcery.MediaManager>();
        if (mediaManager != null)
        {
            Debug.Log($"✅ MediaManager: {mediaManager.gameObject.name}");
        }
        else
        {
            Debug.LogError("❌ 未找到MediaManager组件");
        }
    }
    
    private void VerifyAudioManager()
    {
        Debug.Log("--- 验证AudioManager ---");
        var audioManager = FindObjectOfType<AudioManager>();
        if (audioManager != null)
        {
            Debug.Log($"✅ AudioManager: {audioManager.gameObject.name}");
            
            // 检查音频会话状态
            bool isSessionStarted = audioManager.IsAudioSessionStarted();
            Debug.Log($"  音频会话状态: {(isSessionStarted ? "已启动" : "未启动")}");
            
            var audioSession = audioManager.GetAudioSession();
            Debug.Log($"  音频会话对象: {(audioSession != null ? "存在" : "不存在")}");
        }
        else
        {
            Debug.LogError("❌ 未找到AudioManager组件");
        }
    }
    
    private void VerifyVideoManager()
    {
        Debug.Log("--- 验证VideoManager ---");
        var videoManager = FindObjectOfType<VideoManager>();
        if (videoManager != null)
        {
            Debug.Log($"✅ VideoManager: {videoManager.gameObject.name}");
        }
        else
        {
            Debug.LogError("❌ 未找到VideoManager组件");
        }
    }
    
    private void VerifyLocalAudioExtrasSource()
    {
        Debug.Log("--- 验证LocalAudioExtrasSource ---");
        var audioSource = FindObjectOfType<LocalAudioExtrasSource>();
        if (audioSource != null)
        {
            Debug.Log($"✅ LocalAudioExtrasSource: {audioSource.gameObject.name}");
            
            // 检查详细状态
            Debug.Log($"  音频源类型: {audioSource.SourceType}");
            Debug.Log($"  是否暂停: {audioSource.IsAudioSourcePaused()}");
            Debug.Log($"  编码订阅者: {audioSource.HasEncodedAudioSubscribers()}");
            
            var formats = audioSource.GetAudioSourceFormats();
            Debug.Log($"  支持格式数量: {formats.Count}");
            foreach (var format in formats)
            {
                Debug.Log($"    - {format.FormatName} (ID: {format.FormatID})");
            }
        }
        else
        {
            Debug.LogError("❌ 未找到LocalAudioExtrasSource组件");
            Debug.LogError("   这可能是因为AudioManager未正确初始化");
        }
    }
    
    private void VerifyAudioExtrasSink()
    {
        Debug.Log("--- 验证AudioExtrasSink ---");
        var audioSink = FindObjectOfType<AudioExtrasSink>();
        if (audioSink != null)
        {
            Debug.Log($"✅ AudioExtrasSink: {audioSink.gameObject.name}");
            
            var formats = audioSink.GetAudioSinkFormats();
            Debug.Log($"  支持格式数量: {formats.Count}");
            foreach (var format in formats)
            {
                Debug.Log($"    - {format.FormatName} (ID: {format.FormatID})");
            }
        }
        else
        {
            Debug.LogError("❌ 未找到AudioExtrasSink组件");
        }
    }
    
    /// <summary>
    /// 在GUI中显示组件状态
    /// </summary>
    void OnGUI()
    {
        GUILayout.BeginArea(new Rect(Screen.width - 320, 10, 300, 200));
        GUILayout.BeginVertical("box");
        
        GUILayout.Label("SIP组件状态");
        
        // SIPClient状态
        var sipClient = FindObjectOfType<SIPClient>();
        GUILayout.Label($"SIPClient: {(sipClient != null ? "✅" : "❌")}");
        
        // MediaManager状态
        var mediaManager = FindObjectOfType<SIPSorcery.MediaManager>();
        GUILayout.Label($"MediaManager: {(mediaManager != null ? "✅" : "❌")}");
        
        // AudioManager状态
        var audioManager = FindObjectOfType<AudioManager>();
        GUILayout.Label($"AudioManager: {(audioManager != null ? "✅" : "❌")}");
        
        // LocalAudioExtrasSource状态
        var audioSource = FindObjectOfType<LocalAudioExtrasSource>();
        GUILayout.Label($"AudioSource: {(audioSource != null ? "✅" : "❌")}");
        
        // AudioExtrasSink状态
        var audioSink = FindObjectOfType<AudioExtrasSink>();
        GUILayout.Label($"AudioSink: {(audioSink != null ? "✅" : "❌")}");
        
        // 验证按钮
        if (GUILayout.Button($"验证组件 ({verifyComponentsKey})"))
        {
            VerifyAllComponents();
        }
        
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
}

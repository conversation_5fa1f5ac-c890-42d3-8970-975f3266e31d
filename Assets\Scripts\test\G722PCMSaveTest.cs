using UnityEngine;
using System.IO;
using System;
using SIPSorcery.Media;

/// <summary>
/// G722解码PCM保存测试工具
/// 用于验证接听后的PCM音频帧保存功能是否正常工作
/// </summary>
public class G722PCMSaveTest : MonoBehaviour
{
    [Header("测试控制")]
    public bool enableTestLogs = true;
    public KeyCode testG722DecodeKey = KeyCode.F9;
    public KeyCode checkSavePathKey = KeyCode.F10;
    public KeyCode generateTestG722Key = KeyCode.F11;
    
    [Header("测试参数")]
    public int testDurationSeconds = 5;
    public float testFrequency = 1000f; // 1kHz测试音频
    
    private AudioExtrasSink _audioSink;
    private bool _isTestRunning = false;
    
    void Start()
    {
        if (enableTestLogs)
        {
            Debug.Log("[G722PCMSaveTest] G722解码PCM保存测试工具已启动");
            Debug.Log($"  - 按 {testG722DecodeKey} 测试G722解码和PCM保存");
            Debug.Log($"  - 按 {checkSavePathKey} 检查音频保存路径");
            Debug.Log($"  - 按 {generateTestG722Key} 生成测试G722数据并解码");
        }
    }
    
    void Update()
    {
        if (Input.GetKeyDown(testG722DecodeKey))
        {
            TestG722DecodeAndSave();
        }
        
        if (Input.GetKeyDown(checkSavePathKey))
        {
            CheckAudioSavePath();
        }
        
        if (Input.GetKeyDown(generateTestG722Key))
        {
            GenerateTestG722Data();
        }
    }
    
    /// <summary>
    /// 测试G722解码和PCM保存功能
    /// </summary>
    private void TestG722DecodeAndSave()
    {
        if (_isTestRunning)
        {
            Debug.LogWarning("[G722PCMSaveTest] 测试已在运行中，请等待完成");
            return;
        }
        
        Debug.Log("[G722PCMSaveTest] 开始测试G722解码和PCM保存功能...");
        
        // 查找AudioExtrasSink组件
        _audioSink = FindObjectOfType<AudioExtrasSink>();
        if (_audioSink == null)
        {
            Debug.LogError("[G722PCMSaveTest] 未找到AudioExtrasSink组件！请确保SIP音频会话已初始化");
            return;
        }
        
        Debug.Log("[G722PCMSaveTest] 找到AudioExtrasSink组件，开始测试...");
        
        // 启动测试协程
        StartCoroutine(RunG722DecodeTest());
    }
    
    /// <summary>
    /// 运行G722解码测试
    /// </summary>
    private System.Collections.IEnumerator RunG722DecodeTest()
    {
        _isTestRunning = true;
        
        try
        {
            // 生成测试G722数据
            byte[] testG722Data = GenerateTestG722AudioData();
            
            if (testG722Data == null || testG722Data.Length == 0)
            {
                Debug.LogError("[G722PCMSaveTest] 生成测试G722数据失败");
                yield break;
            }
            
            Debug.Log($"[G722PCMSaveTest] 生成了{testG722Data.Length}字节的测试G722数据");
            
            // 模拟接收多个G722音频帧
            int frameCount = testDurationSeconds * 50; // 50帧/秒 (20ms每帧)
            int frameSize = 160; // G722标准帧大小
            
            for (int i = 0; i < frameCount; i++)
            {
                // 从测试数据中提取一帧
                int startIndex = (i * frameSize) % testG722Data.Length;
                int actualFrameSize = Math.Min(frameSize, testG722Data.Length - startIndex);
                
                byte[] frameData = new byte[actualFrameSize];
                Array.Copy(testG722Data, startIndex, frameData, 0, actualFrameSize);
                
                // 如果数据不足一帧，用前面的数据补充
                if (actualFrameSize < frameSize)
                {
                    byte[] fullFrame = new byte[frameSize];
                    Array.Copy(frameData, fullFrame, actualFrameSize);
                    
                    // 用循环数据填充剩余部分
                    for (int j = actualFrameSize; j < frameSize; j++)
                    {
                        fullFrame[j] = testG722Data[j % testG722Data.Length];
                    }
                    frameData = fullFrame;
                }
                
                // 发送到AudioExtrasSink进行解码和保存
                _audioSink.GotAudioRtp(frameData);
                
                // 每秒输出一次进度
                if (i % 50 == 0)
                {
                    float progress = (float)i / frameCount * 100f;
                    Debug.Log($"[G722PCMSaveTest] 测试进度: {progress:F1}% ({i}/{frameCount}帧)");
                }
                
                // 等待20ms模拟真实的RTP包间隔
                yield return new WaitForSeconds(0.02f);
            }
            
            Debug.Log($"[G722PCMSaveTest] 测试完成！已发送{frameCount}帧G722数据进行解码和保存");
            Debug.Log("[G722PCMSaveTest] 请检查Unity Console中的音频保存日志和保存路径中的PCM文件");
        }
        finally
        {
            _isTestRunning = false;
        }
    }
    
    /// <summary>
    /// 生成测试G722音频数据
    /// </summary>
    private byte[] GenerateTestG722AudioData()
    {
        try
        {
            // 生成16kHz PCM测试音频（1kHz正弦波）
            int sampleRate = 16000;
            int durationSamples = sampleRate * testDurationSeconds;
            short[] pcmData = new short[durationSamples];
            
            for (int i = 0; i < durationSamples; i++)
            {
                float time = (float)i / sampleRate;
                float amplitude = 0.3f; // 30%音量避免削波
                float sample = amplitude * Mathf.Sin(2 * Mathf.PI * testFrequency * time);
                pcmData[i] = (short)(sample * 32767f);
            }
            
            Debug.Log($"[G722PCMSaveTest] 生成了{durationSamples}个PCM样本的测试音频");
            
            // 使用G722编码器编码PCM数据
            var g722Encoder = new G722Codec();
            var g722EncoderState = new G722CodecState(64000, G722Flags.None);
            
            // G722编码缓冲区
            byte[] encodedBuffer = new byte[durationSamples / 2]; // G722压缩比约2:1
            int encodedLength = g722Encoder.Encode(g722EncoderState, encodedBuffer, pcmData, pcmData.Length);
            
            if (encodedLength > 0)
            {
                byte[] result = new byte[encodedLength];
                Array.Copy(encodedBuffer, result, encodedLength);
                Debug.Log($"[G722PCMSaveTest] G722编码成功：{pcmData.Length}样本 -> {encodedLength}字节");
                return result;
            }
            else
            {
                Debug.LogError("[G722PCMSaveTest] G722编码失败");
                return null;
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"[G722PCMSaveTest] 生成测试G722数据异常: {ex.Message}");
            return null;
        }
    }
    
    /// <summary>
    /// 生成测试G722数据（简化版本）
    /// </summary>
    private void GenerateTestG722Data()
    {
        Debug.Log("[G722PCMSaveTest] 生成单帧测试G722数据...");
        
        byte[] testFrame = GenerateTestG722AudioData();
        if (testFrame != null && testFrame.Length > 0)
        {
            // 取前160字节作为一帧测试
            int frameSize = Math.Min(160, testFrame.Length);
            byte[] singleFrame = new byte[frameSize];
            Array.Copy(testFrame, singleFrame, frameSize);
            
            // 查找AudioExtrasSink
            var audioSink = FindObjectOfType<AudioExtrasSink>();
            if (audioSink != null)
            {
                audioSink.GotAudioRtp(singleFrame);
                Debug.Log($"[G722PCMSaveTest] 已发送{frameSize}字节的测试G722帧进行解码");
            }
            else
            {
                Debug.LogError("[G722PCMSaveTest] 未找到AudioExtrasSink组件");
            }
        }
    }
    
    /// <summary>
    /// 检查音频保存路径
    /// </summary>
    private void CheckAudioSavePath()
    {
        string audioSavePath = Path.Combine(Application.persistentDataPath, "AudioDebug");
        
        Debug.Log("[G722PCMSaveTest] 音频保存路径信息:");
        Debug.Log($"  - 保存路径: {audioSavePath}");
        Debug.Log($"  - 路径存在: {Directory.Exists(audioSavePath)}");
        
        if (Directory.Exists(audioSavePath))
        {
            string[] wavFiles = Directory.GetFiles(audioSavePath, "*.wav");
            Debug.Log($"  - WAV文件数量: {wavFiles.Length}");
            
            foreach (string file in wavFiles)
            {
                FileInfo fileInfo = new FileInfo(file);
                Debug.Log($"    * {Path.GetFileName(file)} ({fileInfo.Length}字节, {fileInfo.LastWriteTime})");
            }
        }
        else
        {
            Debug.Log("  - 目录不存在，将在首次保存时创建");
        }
        
        // 在Windows资源管理器中打开路径（仅在Windows编辑器中有效）
        #if UNITY_EDITOR_WIN
        if (Directory.Exists(audioSavePath))
        {
            System.Diagnostics.Process.Start("explorer.exe", audioSavePath.Replace('/', '\\'));
            Debug.Log("[G722PCMSaveTest] 已在资源管理器中打开保存路径");
        }
        #endif
    }
}
